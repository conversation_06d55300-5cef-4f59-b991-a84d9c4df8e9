import "react-native-gesture-handler";
import { Provider } from "react-redux";
import store, { persistor } from "./src/redux/store";
import { PersistGate } from "redux-persist/lib/integration/react";
import { ActivityIndicator, <PERSON><PERSON>, Text, View } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import RootNavigation from "./src/RootNavigation";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { useEffect } from "react";
import Toast from "react-native-toast-message";
import { showToast, toastConfig } from "./src/utils/toastConfig";

const configureGoogleSignIn = () => {
  GoogleSignin.configure({
    webClientId:
      "409646423765-7gn95366la6420f460gnj822tsg27h18.apps.googleusercontent.com", // Replace with your Web Client ID from the Console.
    iosClientId:
      "409646423765-irsip3nkllhuqc7fkmqjtmon3i0s3n22.apps.googleusercontent.com",
    scopes: ["https://www.googleapis.com/auth/contacts"],
    offlineAccess: false, // set to true if you need server-side refresh token.
  });
};

const App = () => {
  useEffect(() => {
    configureGoogleSignIn();
  }, []);

  Text.defaultProps = Text.defaultProps || {};
  Text.defaultProps.allowFontScaling = false;
  // fontFamily: Metropolis-Regular
  Text.defaultProps.style = {
    fontFamily: "Metropolis-Regular",
  };


  return (
    <>
      <Provider store={store}>
        <PersistGate loading={<ActivityIndicator />} persistor={persistor}>
          <NavigationContainer>
            <RootNavigation />
          </NavigationContainer>
        </PersistGate>
      </Provider>
      <Toast config={toastConfig} />
    </>
  );
};

export default App;

// import React, { useState } from 'react';
// import { View, Text, Button, FlatList, Alert } from 'react-native';
// import { fetchMicrosoftContacts, signInAndGetToken, updateMicrosoftContact } from './src/utils/commonHelpers';

// const App = () => {
//   const [accessToken, setAccessToken] = useState(null);
//   const [contacts, setContacts] = useState([]);
//   const [error, setError] = useState('');

//   const handleSignInAndFetch = async () => {
//     try {
//       const token = await signInAndGetToken();
//       setAccessToken(token);
//       const contactsData = await fetchMicrosoftContacts(token);
//       setContacts(contactsData);
//     } catch (err) {
//       setError('Error: ' + err.message);
//     }
//   };

//   const handleUpdateContact = async (contact) => {
//     // For example, update the displayName while preserving the mobilePhone if available
//     const updatedPayload = {
//       displayName: 'Updated Display Name',
//       // Merge any other fields you want to preserve:
//       mobilePhone: contact.mobilePhone 
//     };
//     try {
//       const updated = await updateMicrosoftContact(contact.id, updatedPayload, accessToken);
//       Alert.alert('Contact updated successfully!');
//     } catch (err) {
//       Alert.alert('Error updating contact: ' + err.message);
//     }
//   };

//   return (
//     <View style={{ flex: 1, padding: 30 }}>
//       <Button title="Sign In and Fetch Contacts" onPress={handleSignInAndFetch} />
//       {error ? <Text style={{ color: 'red' }}>{error}</Text> : null}
//       <FlatList
//         data={contacts}
//         keyExtractor={(item, index) => item.id.toString() || index.toString()}
//         renderItem={({ item }) => (
//           <View style={{ marginVertical: 8, borderBottomWidth: 1, paddingBottom: 8 }}>
//             <Text style={{ fontWeight: 'bold' }}>{item.displayName}</Text>
//             {item.emailAddresses &&
//               item.emailAddresses.map((email, i) => (
//                 <Text key={i}>{email.address}</Text>
//               ))}
//             {item.mobilePhone && <Text>Phone: {item.mobilePhone}</Text>}
//             <Button title="Update Display Name" onPress={() => handleUpdateContact(item)} />
//           </View>
//         )}
//       />
//     </View>
//   );
// };

// export default App;

