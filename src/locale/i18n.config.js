import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import { en, fr } from "./translations"; // Assuming you have a translations folder

const resources = {
  en: { translation: en },
  fr: { translation: fr },
};

i18next.use(initReactI18next).init({
  compatibilityJSON: 'v3',
  fallbackLng: 'en',
  lng: 'en', // Default language
  debug: true, // Set to false in production
  resources, // Pass the translation resources
  interpolation: {
    escapeValue: false, // React already escapes the content
  },
});

export default i18next;