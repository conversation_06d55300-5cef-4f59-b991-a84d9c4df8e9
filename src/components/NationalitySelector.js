import React from "react";
import { TouchableOpacity } from "react-native";
import CountryPicker from "react-native-country-picker-modal";
import MyText from "./MyText";
import colors from "../assets/colors";

const NationalitySelector = ({
  value,
  onSelect,
  error,
  isPickerVisible,
  setPickerVisible,
}) => {
  return (
    <>
      <MyText p medium style={{ marginBottom: 8 }}>
        Nationality*
      </MyText>
      <TouchableOpacity
        style={{
          backgroundColor: colors.inputBg,
          borderRadius: 12,
          marginBottom: 16,
          height: 48,
          justifyContent: "center",
          paddingHorizontal: 12,
        }}
        onPress={() => setPickerVisible(true)}
      >
        <MyText
          style={{
            color: value ? colors.black : colors.txtGray,
          }}
          p
        >
          {value || "Select Nationality"}
        </MyText>
      </TouchableOpacity>
      <CountryPicker
        withFilter
        withFlag
        withEmoji={true}
        onSelect={(country) => {
          onSelect(country.name);
          setPickerVisible(false);
        }}
        visible={isPickerVisible}
        onClose={() => setPickerVisible(false)}
        renderFlagButton={() => null}
      />
    </>
  );
};

export default NationalitySelector;
