import React from "react";
import { View, TouchableOpacity, StyleSheet, Platform } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import colors from "../assets/colors";

const CustomTabBarButton = ({ children, onPress }) => (
  <TouchableOpacity
    style={styles.container}
    activeOpacity={0.8}
    onPress={onPress}
  >
    <LinearGradient colors={colors?.gradient} style={styles.button}>
      {children}
    </LinearGradient>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    top: -20,
    justifyContent: "center",
    alignItems: "center",
    ...Platform.select({
      android: { elevation: 5 },
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.12,
        shadowRadius: 5,
      },
    }),
  },
  button: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#2E64FE",
    justifyContent: "center",
    alignItems: "center",
  },
});

export default CustomTabBarButton;
