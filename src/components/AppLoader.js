import { ActivityIndicator, Modal, StyleSheet, Text, View } from "react-native";
import React from "react";
import { SCREEN_HEIGHT } from "../utils/constants";
import colors from "../assets/colors";

const AppLoader = ({ isLoading }) => {
  return (
    <Modal animationType={"fade"} visible={isLoading} transparent>
      <View style={styles.loadingContainer}>
        <View style={styles.loader}>
          <ActivityIndicator size={"large"} color={colors.primary} />
        </View>
      </View>
    </Modal>
  );
};

export default AppLoader;

const styles = StyleSheet.create({
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.2)",
    zIndex: 1000,
  },
  loader: {
    padding: 20,
    borderRadius: 8,
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
