import React from "react";
import { View, StyleSheet } from "react-native";
import Svg, { Path } from "react-native-svg";
import colors from "../assets/colors";
import { SCREEN_WIDTH } from "../utils/constants";

const R = 30; // corner radius
const CURVE_DEPTH = 35; // bump height
const TAB_BAR_HEIGHT = 100; // flat bottom
const TOTAL_HEIGHT = CURVE_DEPTH + TAB_BAR_HEIGHT;

export default function CustomTabBarBackground({ style }) {
  // Get the current screen width dynamically to ensure it always matches
  const width = SCREEN_WIDTH;

  // Calculate proportional values based on screen width
  const leftCurveStart = width * 0.32; // 32% of screen width
  const centerPoint = width * 0.5; // 50% of screen width
  const rightCurveEnd = width * 0.68; // 68% of screen width

  return (
    <View style={[styles.container, { height: TOTAL_HEIGHT }, style]}>
      <Svg
        width="100%"
        height={TOTAL_HEIGHT}
        viewBox={`0 0 ${width} ${TOTAL_HEIGHT}`}
        preserveAspectRatio="xMidYMid meet"
      >
        <Path
          d={`
            M0,${CURVE_DEPTH + R}
            A${R},${R} 0 0 1 ${R},${CURVE_DEPTH}
            H${leftCurveStart}
            C${leftCurveStart + 20},${CURVE_DEPTH} ${
            leftCurveStart + 20
          },0 ${centerPoint},0
            C${rightCurveEnd - 20},0 ${
            rightCurveEnd - 20
          },${CURVE_DEPTH} ${rightCurveEnd},${CURVE_DEPTH}
            H${width - R}
            A${R},${R} 0 0 1 ${width},${CURVE_DEPTH + R}
            V${TOTAL_HEIGHT}
            H0
            Z
          `}
          fill="#fff"
          stroke={colors.gray}
        />
      </Svg>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    // If you like, you can still round the RN View:
    borderTopLeftRadius: R,
    borderTopRightRadius: R,
    overflow: "hidden",
  },
});
