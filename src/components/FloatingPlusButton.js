import React from 'react';
import { View, TouchableOpacity, Image, StyleSheet, Dimensions, Alert } from 'react-native';
import icons from '../assets/icons';
import colors from '../assets/colors';

const screenHeight = Dimensions.get('window').height;
const bottomPosition = parseInt(screenHeight * 0.135);

const FloatingPlusButton = ({ onPress }) => {
    
    
  return (
    <TouchableOpacity onPress={onPress} style={styles.button}>
      <Image source={icons.plusIconBlack} resizeMode='contain' style={styles.icon} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    bottom: bottomPosition,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    zIndex: 1000,
  },
  icon: {
    width: 56,
    height: 56,
  },
});



export default FloatingPlusButton;
