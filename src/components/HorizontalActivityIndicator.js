import React, { useEffect, useState, useRef } from "react";
import { View, Animated, StyleSheet, Text } from "react-native";
import colors from "../assets/colors";
import { SCREEN_WIDTH } from "../utils/constants";
import MyText from "./MyText";

const HorizontalActivityIndicator = ({
  isLoading,
  progress: externalProgress,
  showPercentage = false,
}) => {
  // Animation value for the width
  const progressAnim = useRef(new Animated.Value(0)).current;
  const [progress, setProgress] = useState(0);

  // State to track start time and duration
  const [startTime, setStartTime] = useState(null);
  const [duration, setDuration] = useState(null);

  // Handle animation based on external progress or loading state
  useEffect(() => {
    if (externalProgress !== undefined) {
      // If external progress is provided, use it
      setProgress(externalProgress);
      Animated.timing(progressAnim, {
        toValue: externalProgress / 100,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else if (isLoading) {
      // If just loading, animate from 0 to 100 in a loop
      setProgress(0);
      progressAnim.setValue(0);

      // Animate from 0 to 95% (we don't go to 100% to indicate it's still loading)
      Animated.timing(progressAnim, {
        toValue: 0.95,
        duration: 3000,
        useNativeDriver: false,
      }).start();

      // Update progress state for display
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 95) {
            clearInterval(interval);
            return 95;
          }
          return prev + 1;
        });
      }, 30);

      return () => clearInterval(interval);
    } else {
      // If not loading, reset to 0
      setProgress(0);
      progressAnim.setValue(0);
    }
  }, [isLoading, externalProgress]);

  // Track duration of isLoading being true
  useEffect(() => {
    if (isLoading) {
      // Record start time when isLoading becomes true
      setStartTime(Date.now());
      setDuration(null); // Reset duration
    } else if (startTime) {
      // Calculate duration when isLoading becomes false
      const endTime = Date.now();
      const calculatedDuration = endTime - startTime;
      setDuration(calculatedDuration);

      // Complete the progress to 100% when loading finishes
      setProgress(100);
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }).start();

      console.log(`isLoading was true for ${calculatedDuration} milliseconds`);
    }
  }, [isLoading]);

  // Calculate the width of the progress bar
  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0%", "100%"],
  });

  return (
    <View style={styles.container}>
      <View style={styles.indicatorContainer}>
        <Animated.View
          style={[
            styles.indicator,
            {
              width: progressWidth,
            },
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    width: SCREEN_WIDTH - 50,
  },
  indicatorContainer: {
    width: "100%",
    height: 10,
    backgroundColor: colors.primaryAlpha(0.2),
    borderRadius: 5,
    overflow: "hidden",
  },
  indicator: {
    height: 10,
    backgroundColor: colors.primary,
    borderRadius: 5,
  },
  percentageText: {
    marginTop: 5,
    color: colors.txtGray,
  },
  durationText: {
    fontSize: 16,
    color: "#333",
    marginTop: 10,
  },
});

export default HorizontalActivityIndicator;
