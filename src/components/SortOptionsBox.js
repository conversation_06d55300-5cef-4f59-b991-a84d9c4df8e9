import React from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import MyText from "./MyText";
import colors from "../assets/colors";

const SortOptionsBox = ({
  options = [],
  onSelect,
  style = {},
  optionStyle = {},
  optionTextStyle = {},
  activeValue,
  allowDeselect = false,
}) => {
  const handleOptionPress = (option) => {
    if (allowDeselect && option.value === activeValue) {
      onSelect(null); // Deselect if already active
    } else {
      onSelect(option);
    }
  };

  return (
    <View style={[styles.box, style]}>
      {options.map((option, idx) => {
        const isActive = option.value === activeValue;
        return (
          <TouchableOpacity
            key={option.value || option.label || idx}
            onPress={() => handleOptionPress(option)}
            style={[
              styles.option,
              optionStyle,
              isActive && { backgroundColor: "#e6f0fa" },
            ]}
          >
            <MyText
              p
              children={option.label}
              semibold={isActive}
              style={[
                styles.optionText,
                optionTextStyle,
                isActive && { color: colors.primaryAlpha(0.8) },
              ]}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  option: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  optionText: {
    alignSelf: "flex-start",
  },
});

export default SortOptionsBox;
