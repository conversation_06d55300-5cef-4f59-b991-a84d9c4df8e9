import React from "react";
import { View, TouchableOpacity, StyleSheet, Image } from "react-native";
import Avatar from "./Avatar";
import MyText from "./MyText";
import colors from "../assets/colors";
import icons from "../assets/icons";

const styles = StyleSheet.create({
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  contactInfo: {
    flex: 1,
    marginLeft: 10,
  },
  radioWrapper: {
    marginLeft: 10,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  radioSelected: {
    borderColor: colors.primary,
    backgroundColor: "white",
  },
  radioInnerCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
});

const ContactCard = ({
  name,
  phone,
  isSelected,
  mode,
  onPress,
  onCancel,
  imgUrl = "",
}) => {
  console.log("🚀 ~ name:", name);
  return (
    <TouchableOpacity
      style={styles.contactItem}
      onPress={onPress}
      disabled={mode !== "select" && mode !== "viewprofile"}
    >
      <Avatar name={name} size={40} url={imgUrl} />
      <View style={styles.contactInfo}>
        <MyText
          h6
          semibold
          children={name}
          style={{ textTransform: "capitalize" }}
        />
        <MyText p children={phone} />
      </View>
      {mode === "select" && (
        <View style={[styles.radioWrapper, isSelected && styles.radioSelected]}>
          {isSelected && <View style={styles.radioInnerCircle} />}
        </View>
      )}
      {mode === "cancel" && (
        <TouchableOpacity
          // style={styles.radioWrapper}
          onPress={onCancel}
        >
          <Image source={icons.closeIcon} style={{ width: 20, height: 20 }} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

export default ContactCard;
