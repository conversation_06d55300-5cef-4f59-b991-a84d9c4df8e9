import React from "react";
import {
  Text as RNText,
  StyleSheet,
  Dimensions,
  PixelRatio,
  useWindowDimensions,
  Platform,
} from "react-native";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_WIDTH = 375; // iPhone 11 width

const MyText = ({
  // Font weight props
  thin = false,
  light = false,
  regular = false,
  medium = false,
  semibold = false,
  bold = false,

  // Heading level props
  h1 = false,
  h2 = false,
  h3 = false,
  h4 = false,
  h5 = false,
  h6 = false,
  p = false,
  tiny = false,

  // Text styling props
  italic = false,
  underline = false,
  strike = false,
  center = false,
  right = false,
  color,
  opacity,

  // Functional props
  allowFontScaling = false,
  maxFontSizeMultiplier = 1.2,
  adjustsFontSizeToFit = false,
  numberOfLines,

  // Style and content
  style,
  children,
  ...props
}) => {
  const { width, fontScale } = useWindowDimensions();

  // Calculate scale factor
  const scale = Math.min(width / BASE_WIDTH, 1.2); // Cap scaling at 1.2x

  // Determine base font size
  let fontSize = 14; // default
  if (h1) fontSize = 32;
  else if (h2) fontSize = 28;
  else if (h3) fontSize = 24;
  else if (h4) fontSize = 20;
  else if (h5) fontSize = 16;
  else if (h6) fontSize = 14;
  else if (p) fontSize = 12;
  else if (tiny) fontSize = 8;

  // Get styles from props
  const flattenedStyle = StyleSheet.flatten(style) || {};

  // Calculate responsive sizes
  const responsiveFontSize = PixelRatio.roundToNearestPixel(
    (flattenedStyle.fontSize || fontSize) *
      scale *
      (allowFontScaling ? fontScale : 1)
  );

  const lineHeight = flattenedStyle.lineHeight || fontSize * 1.4;
  const responsiveLineHeight = PixelRatio.roundToNearestPixel(
    lineHeight * scale
  );

  const letterSpacing = flattenedStyle.letterSpacing || 0;
  const responsiveLetterSpacing = PixelRatio.roundToNearestPixel(
    letterSpacing * scale
  );

  // Determine font family
  let fontFamily = "Metropolis-Regular"; // default
  if (thin) fontFamily = "Metropolis-Thin";
  else if (light) fontFamily = "Metropolis-Light";
  else if (regular) fontFamily = "Metropolis-Regular";
  else if (medium) fontFamily = "Metropolis-Medium";
  else if (semibold) fontFamily = "Metropolis-SemiBold";
  else if (bold) fontFamily = "Metropolis-Bold";

  // Text alignment
  let textAlign = "left";
  if (center) textAlign = "center";
  else if (right) textAlign = "right";

  return (
    <RNText
      allowFontScaling={allowFontScaling}
      maxFontSizeMultiplier={maxFontSizeMultiplier}
      adjustsFontSizeToFit={adjustsFontSizeToFit}
      numberOfLines={numberOfLines}
      style={[
        styles.base,
        {
          fontFamily,
          fontSize: responsiveFontSize,
          lineHeight: responsiveLineHeight,
          letterSpacing: responsiveLetterSpacing,
          textAlign,
        },
        h1 && styles.h1,
        h2 && styles.h2,
        h3 && styles.h3,
        h4 && styles.h4,
        h5 && styles.h5,
        h6 && styles.h6,
        p && styles.p,
        italic && styles.italic,
        underline && styles.underline,
        strike && styles.strike,
        color && { color },
        opacity && { opacity },
        flattenedStyle, // Apply any additional styles last
      ]}
      {...props}
    >
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  base: {
    color: "#000000",
    includeFontPadding: false,
    textAlignVertical: "center",
  },
  h1: {
    fontWeight: Platform.OS === "ios" ? "700" : undefined,
    letterSpacing: -0.5,
  },
  h2: {
    fontWeight: Platform.OS === "ios" ? "600" : undefined,
    letterSpacing: -0.3,
  },
  h3: {
    fontWeight: Platform.OS === "ios" ? "500" : undefined,
    letterSpacing: -0.2,
  },
  h4: {
    letterSpacing: -0.1,
  },
  h5: {
    letterSpacing: 0,
  },
  h6: {
    letterSpacing: 0.1,
  },
  p: {
    letterSpacing: 0.2,
  },
  italic: {
    fontStyle: "italic",
  },
  underline: {
    textDecorationLine: "underline",
  },
  strike: {
    textDecorationLine: "line-through",
  },
});

export default React.memo(MyText);

//Usage

// Basic usage
{
  /* <MyText>Default regular text</MyText> */
}

// Font weights
{
  /* <MyText thin>Thin weight</MyText>
<MyText light>Light weight</MyText>
<MyText medium>Medium weight</MyText>
<MyText semibold>SemiBold weight</MyText>
<MyText bold>Bold weight</MyText> */
}

// Headings
{
  /* <MyText h1 bold>Heading 1</MyText>
<MyText h2 semibold>Heading 2</MyText>
<MyText h3 medium>Heading 3</MyText> */
}

// Paragraphs
{
  /* <MyText p>This is a paragraph</MyText> */
}

// Styled text
{
  /* <MyText 
  h4 
  light 
  italic 
  center 
  color="#FF5733" 
  style={{ marginTop: 10 }}
>
  Styled text with custom properties
</MyText> */
}

// Text with limited lines
{
  /* <MyText 
  numberOfLines={2} 
  adjustsFontSizeToFit
>
  Long text that will be truncated after 2 lines and may adjust font size
</MyText> */
}
