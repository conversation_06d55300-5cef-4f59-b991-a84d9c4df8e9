import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import PropTypes from 'prop-types';
import colors from '../assets/colors';

/**
 * AddressTypeSelector - A specialized chip selector for address types (Home/Work)
 * 
 * @param {string} selectedType - Currently selected address type ('home' or 'work')
 * @param {function} onSelect - Function called when an option is selected
 * @param {Object} containerStyle - Additional styles for the container
 */
const AddressTypeSelector = ({
  selectedType,
  onSelect,
  containerStyle,
}) => {
  // Home icon - a simple house icon using SVG paths
  const HomeIcon = ({ isActive }) => (
    <View style={styles.iconWrapper}>
      <View style={[
        styles.homeIcon, 
        { borderColor: isActive ? colors.primary : '#9E9E9E' }
      ]}>
        <View style={[
          styles.homeRoof, 
          { backgroundColor: isActive ? colors.primary : '#9E9E9E' }
        ]} />
      </View>
    </View>
  );

  // Work icon - a simple briefcase icon
  const WorkIcon = ({ isActive }) => (
    <View style={styles.iconWrapper}>
      <View style={[
        styles.briefcase, 
        { borderColor: isActive ? colors.primary : '#9E9E9E' }
      ]}>
        <View style={[
          styles.briefcaseHandle, 
          { backgroundColor: isActive ? colors.primary : '#9E9E9E' }
        ]} />
      </View>
    </View>
  );

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={[
          styles.chip,
          selectedType === 'home' ? styles.inactiveChip : styles.inactiveChip,
          selectedType === 'home' && styles.activeChip,
        ]}
        onPress={() => onSelect('home')}
        activeOpacity={0.7}
      >
        <HomeIcon isActive={selectedType === 'home'} />
        <Text
          style={[
            styles.text,
            selectedType === 'home' ? styles.activeText : styles.inactiveText,
          ]}
        >
          Home
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.chip,
          selectedType === 'work' ? styles.inactiveChip : styles.inactiveChip,
          selectedType === 'work' && styles.activeChip,
        ]}
        onPress={() => onSelect('work')}
        activeOpacity={0.7}
      >
        <WorkIcon isActive={selectedType === 'work'} />
        <Text
          style={[
            styles.text,
            selectedType === 'work' ? styles.activeText : styles.inactiveText,
          ]}
        >
          Work
        </Text>
      </TouchableOpacity>
    </View>
  );
};

AddressTypeSelector.propTypes = {
  selectedType: PropTypes.oneOf(['home', 'work']).isRequired,
  onSelect: PropTypes.func.isRequired,
  containerStyle: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
    width: '100%',
    maxWidth: 600,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 100,
    flex: 1,
    marginHorizontal: 8,
  },
  activeChip: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: colors.primary,
  },
  inactiveChip: {
    backgroundColor: '#F5F7FA',
    borderWidth: 1,
    borderColor: '#F5F7FA',
  },
  text: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  activeText: {
    color: '#000',
  },
  inactiveText: {
    color: '#9E9E9E',
  },
  iconWrapper: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Home icon styles
  homeIcon: {
    width: 18,
    height: 16,
    borderWidth: 2,
    borderTopWidth: 0,
    borderRadius: 2,
    position: 'relative',
  },
  homeRoof: {
    width: 22,
    height: 10,
    position: 'absolute',
    top: -8,
    left: -4,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  // Work icon styles
  briefcase: {
    width: 18,
    height: 16,
    borderWidth: 2,
    borderRadius: 2,
    position: 'relative',
  },
  briefcaseHandle: {
    width: 8,
    height: 4,
    position: 'absolute',
    top: -4,
    left: 3,
    borderTopLeftRadius: 2,
    borderTopRightRadius: 2,
  },
});

export default AddressTypeSelector;
