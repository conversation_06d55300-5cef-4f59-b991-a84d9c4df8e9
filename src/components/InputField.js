import React from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
} from "react-native";
import MyText from "./MyText";
import commonStyles from "../assets/commonStyles";
import colors from "../assets/colors";
import icons from "../assets/icons";

const InputField = ({
  label,
  placeholder,
  secure = false,
  rightIcon,
  value,
  onChangeText,
  error,
  disabled = false,
  keyboardType = "default",
  showVisibilityToggle = false,
  isVisible = false,
  onVisibilityToggle,
  maxLength = 100,
  ...textInputProps // Accept all other TextInput props
}) => (
  <View style={styles.container}>
    {label && (
      <MyText p medium style={styles.label}>
        {label}
      </MyText>
    )}
    <View style={[styles.inputWrapper, error && styles.errorBorder]}>
      <TextInput
        value={value}
        maxLength={maxLength}
        editable={!disabled}
        selectTextOnFocus={!disabled}
        onChangeText={onChangeText}
        placeholder={placeholder}
        secureTextEntry={secure}
        keyboardType={keyboardType}
        style={[
          styles.input,
          
          commonStyles?.text,
          showVisibilityToggle && { paddingRight: 40 }, // Add padding for the eye icon
          disabled && { color: "gray" },
        ]}
        placeholderTextColor="#7D7D7D"
        {...textInputProps} // Spread all additional TextInput props
      />
      {showVisibilityToggle && (
        <TouchableOpacity
          style={styles.visibilityToggle}
          onPress={onVisibilityToggle}
          activeOpacity={0.7}
          disabled={disabled}
        >
          <Image
            source={
              isVisible ? icons.openEyeProfileIcon : icons.closedEyeProfileIcon
            }
            style={styles.visibilityIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      )}
      {rightIcon && <View style={styles.icon}>{rightIcon}</View>}
    </View>
    {error && (
      <MyText p color={colors?.errorRed} style={styles.errorText}>
        {error}
      </MyText>
    )}
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 6,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f2f2f2",
    borderRadius: 10,
    paddingHorizontal: 12,
    position: "relative",
  },
  input: {
    flex: 1,
    height: 48,
  },
  icon: {
    marginLeft: 10,
  },
  errorBorder: {
    borderColor: "red",
    borderWidth: 1,
  },
  errorText: {
    marginTop: 4,
  },
  visibilityToggle: {
    position: "absolute",
    right: 12,
    padding: 5,
    zIndex: 1,
  },
  visibilityIcon: {
    width: 20,
    height: 20,
  },
});

export default InputField;
