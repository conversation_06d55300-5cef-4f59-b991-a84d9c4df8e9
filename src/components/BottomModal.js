import React from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Dimensions,
  Image,
} from "react-native";
import {
  GestureHandlerRootView,
  PanGestureHandler,
} from "react-native-gesture-handler";
import MyText from "./MyText";
import icons from "../assets/icons";
import commonStyles from "../assets/commonStyles";
import colors from "../assets/colors";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

const BottomModal = ({ isVisible, onClose, title, children }) => {
  const translateY = React.useRef(0).current;

  const handleGesture = ({ nativeEvent }) => {
    if (nativeEvent.translationY > 100) {
      onClose();
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <GestureHandlerRootView style={styles.container}>
        {/* <PanGestureHandler onGestureEvent={handleGesture}> */}
          <View style={styles.overlay}>
            <TouchableOpacity style={styles.overlay} onPress={onClose} />
            <View style={styles.modalContent}>
              <View style={styles.header}>
                <MyText h5 semibold children={title} />
                {/* <Text style={styles.title}>{title}</Text> */}
                <TouchableOpacity onPress={onClose}>
                  <Image
                    source={icons.closeIcon}
                    style={commonStyles?.smallIcon}
                  />
                </TouchableOpacity>
              </View>
              <View style={styles.content}>{children}</View>
            </View>
          </View>
        {/* </PanGestureHandler> */}
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    padding: 16,
    maxHeight: SCREEN_HEIGHT * 0.5,
    width: "100%",
    position: "absolute",
    bottom: 0,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    fontSize: 18,
    color: "#666",
  },
  content: {
    flex: 1,
  },
});

export default BottomModal;
