import FastImage from "@d11/react-native-fast-image";
import { StyleSheet, TouchableOpacity } from "react-native";
import commonStyles from "../assets/commonStyles";
import icons from "../assets/icons";
import { useNavigation } from "@react-navigation/native";
import colors from "../assets/colors";

const BackButton = () => {
    const navigation = useNavigation();
    return (
        <TouchableOpacity style={styles.backContainer} onPress={() => navigation.goBack()}>
            <FastImage
                style={[commonStyles.smallIcon, { tintColor: "#6F6F6F" }]}
                source={icons.backIcon}
                resizeMode={FastImage.resizeMode.contain}
            />
        </TouchableOpacity>
    );
}

export default BackButton;

const styles = StyleSheet.create({
    backContainer: {
        borderWidth: 0.5,
        borderRadius: 12,
        alignSelf: 'flex-start',
        padding: 10,
        borderColor: colors.darkGray,
    },
});
