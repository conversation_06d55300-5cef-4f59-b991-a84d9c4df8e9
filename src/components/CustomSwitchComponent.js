import React, { useEffect, useState } from 'react';
import { View, Text, Switch, StyleSheet } from 'react-native';
import colors from '../assets/colors';
import commonStyles from '../assets/commonStyles';

const CustomSwitchComponent = ({ label, onToggle, value=false, update }) => {
    const [isEnabled, setIsEnabled] = useState(value);


    useEffect(() => {
        setIsEnabled(value);
    }
    , [value, update]);

    const toggleSwitch = () => {
        const newValue = !isEnabled;
        setIsEnabled(newValue);
        onToggle(newValue); // Trigger the API call or action
    };

    return (
        <View style={styles.container}>
            <Text style={styles.label}>{label}</Text>
            <Switch
                trackColor={{ false: colors.gray, true: colors.gray }}
                thumbColor={isEnabled ? colors.primary : "darkgray"}
                onValueChange={toggleSwitch}
                value={isEnabled}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: 10,
    },
    label: {
        fontSize: 14,
        color: 'black',
        ...commonStyles.text,
    },
});

export default CustomSwitchComponent;
