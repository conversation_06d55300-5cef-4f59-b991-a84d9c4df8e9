import React, { useState } from "react";
import {
  ActivityIndicator,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
} from "react-native";
import { useTranslation } from "react-i18next";
import colors from "../assets/colors";
import MyText from "./MyText";
import commonStyles from "../assets/commonStyles";

export const PrimaryButton = ({
  style,
  textStyle,
  title,
  rightIcon,
  loading,
  disabled,
  ...props
}) => {
  return (
    <TouchableOpacity
      {...props}
      disabled={disabled || loading}
      style={[
        styles.button,
        style,
        { flexDirection: "row" },
        commonStyles?.center,
      ]}
      activeOpacity={0.6}
      accessible={true}
      accessibilityLabel={title}
      accessibilityRole="button"
    >
      {loading ? (
        <ActivityIndicator size={"small"} color={colors?.white} />
      ) : (
        <MyText
          color={textStyle?.color || colors.white}
          medium
          children={title}
        />
      )}
      {rightIcon && (
        <Image
          resizeMode="contain"
          source={rightIcon}
          style={{ height: 25, width: 25 }}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    width: 329,
    alignSelf: "center",
    height: 60,
    borderRadius: 15,
    backgroundColor: colors.primary,
  },

  iconBtn: {
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: colors.lightgreay,
    borderRadius: 8,
    marginVertical: 10,
  },
});
