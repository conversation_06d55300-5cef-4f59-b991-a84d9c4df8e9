import {
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
  StatusBar,
  Dimensions,
} from "react-native";
import commonStyles from "../assets/commonStyles";
import LinearGradient from "react-native-linear-gradient";
import MyText from "./MyText";
import Avatar from "./Avatar";
import colors from "../assets/colors";
import { useSelector } from "react-redux";

const Header = ({
  title,
  leftIcon,
  onPressLeft,
  rightIcon1,
  rightIcon2,
  textCenter,
  isAvatar,
  onPressRight,
  onPressRight2,
  pb = 10,
  noStatusBar,
}) => {
  // Calculate status bar height dynamically
  const statusBarHeight =
    Platform.OS === "android"
      ? StatusBar.currentHeight
      : Dimensions.get("window").height >= 812
      ? 44
      : 20;

  const user = useSelector((state) => state.auth.user);

  return (
    <LinearGradient colors={colors?.gradient}>
      {/* StatusBar (transparent, handled by gradient) */}
      {!noStatusBar && (
        <StatusBar
          backgroundColor="transparent"
          translucent
          barStyle="light-content"
        />
      )}

      {/* Header Content */}
      <View
        style={[
          styles.container,
          styles.row,
          {
            paddingTop: statusBarHeight + 30,
            paddingBottom: pb,
          },
        ]}
      >
        {/* Left Side */}
        <View style={styles.side}>
          <TouchableOpacity onPress={onPressLeft}>
            {leftIcon && (
              <Image
                source={leftIcon}
                resizeMode="contain"
                style={commonStyles.header.midIcon}
              />
            )}
          </TouchableOpacity>
        </View>

        {/* Center */}
        <View
          style={[
            styles.center,
            textCenter ? styles.centerAligned : styles.leftAligned,
          ]}
        >
          <MyText
            bold
            color={colors?.white}
            h5
            center={textCenter}
            style={[
              styles.text,
              textCenter ? { textAlign: "center" } : { textAlign: "left" },
            ]}
            children={title}
          />
        </View>

        {/* Right Side */}
        <View style={styles.side}>
          {rightIcon1 && (
            <TouchableOpacity onPress={onPressRight}>
              <Image
                source={rightIcon1}
                resizeMode="contain"
                style={commonStyles.header.midIcon}
              />
            </TouchableOpacity>
          )}
          {rightIcon2 && (
            <TouchableOpacity onPress={onPressRight2}>
              <Image
                source={rightIcon2}
                resizeMode="contain"
                style={commonStyles.header.midIcon}
              />
            </TouchableOpacity>
          )}
          {isAvatar && (
            <Avatar
              url={user?.profile_image}
              name={user?.firstName}
              size={40}
              onPress={onPressRight2}
            />
          )}
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  side: {
    width: 50,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: 5,
  },
  center: {
    flex: 1,
  },
  centerAligned: {
    alignItems: "center",
  },
  leftAligned: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    paddingLeft: 10,
  },
  text: {
    width: "100%",
  },
});

export default Header;
