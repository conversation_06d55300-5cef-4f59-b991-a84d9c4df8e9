import React, { useEffect, useState } from "react";
import { View, Text, Modal, StyleSheet } from "react-native";
import Animated, {
  Easing,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import colors from "../assets/colors";
import MyText from "./MyText";
import commonStyles from "../assets/commonStyles";
import { PrimaryButton } from "./Button";

const ImportLoadingModal = ({ isVisible, onPress, duration, onCancel }) => {
  const [progress, setProgress] = useState(0);
  const animatedProgress = useSharedValue(0);

  useEffect(() => {
    const effectiveDuration = Math.max(duration, 3); // Ensure minimum duration of 3 seconds

    if (effectiveDuration) {
      setProgress(0);
      animatedProgress.value = 0;
      const startTime = Date.now();

      const interval = setInterval(() => {
        const elapsedTime = (Date.now() - startTime) / 1000; // Calculate elapsed time in seconds
        const newProgress = Math.min(
          (elapsedTime / effectiveDuration) * 100,
          100
        ); // Use effectiveDuration for full progress

        setProgress(newProgress);

        if (newProgress >= 100) {
          clearInterval(interval);
        }
      }, 100); // Update every 100ms

      return () => clearInterval(interval);
    }
  }, [duration]);

  useEffect(() => {
    animatedProgress.value = withTiming(progress, {
      duration: 100,
      easing: Easing.inOut(Easing.ease),
    });
  }, [progress]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: `${animatedProgress.value}%`,
    };
  });

  if (!isVisible) return null;

  return (
    <Modal animationType={"fade"} visible={isVisible} transparent>
      <View style={[styles.modalContainer, commonStyles?.center]}>
        <View style={styles.container}>
          <MyText h5 semibold children={"Importing Contacts"} style={{}} />
          <View style={{ paddingHorizontal: 14, paddingVertical: 16 }}>
            <View style={styles.progressContainer}>
              <Animated.View style={[styles.progressBar, animatedStyle]} />
            </View>
            <MyText
              p
              regular
              center
              children={`${Math.round(progress)}/100 imported`}
              style={{ marginTop: 10 }}
            />
          </View>
          <View style={commonStyles?.rowWithoutSpaceBetween}>
            <PrimaryButton
              onPress={onPress}
              title={"Done"}
              style={[styles.doneBtn]}
            />
            <PrimaryButton
              onPress={onCancel}
              title={"Cancel"}
              style={[styles.cancelButton]}
              textStyle={{ color: colors.primary }}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  container: {
    justifyContent: "center",
    alignItems: "center",
    width: "85%",
    backgroundColor: colors.white,
    borderRadius: 20,
    alignSelf: "center",
    paddingVertical: 20,
  },
  progressContainer: {
    width: "100%",
    height: 10,
    backgroundColor: colors.lightGray,
    borderRadius: 5,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 5,
  },
  doneBtn: {
    backgroundColor: colors.primary,
    width: "50%",
    marginTop: 10,
  },
  cancelButton: {
    backgroundColor: "transparent",
    width: "30%",
    marginTop: 10,
    borderWidth: 1,
    borderColor: colors.primary,
  },
});

export default ImportLoadingModal;
