import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import Svg, { Path, Rect } from 'react-native-svg';

/**
 * ExactChipSelector - A chip selector that exactly matches the provided image
 * 
 * @param {string} selectedValue - Currently selected value ('home' or 'work')
 * @param {function} onSelect - Function called when an option is selected
 * @param {Object} containerStyle - Additional styles for the container
 */
const ExactChipSelector = ({
  selectedValue,
  onSelect,
  containerStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      {/* Home Chip */}
      <TouchableOpacity
        style={[
          styles.chip,
          selectedValue === 'home' ? styles.activeChip : styles.inactiveChip,
        ]}
        onPress={() => onSelect('home')}
        activeOpacity={0.7}
      >
        <View style={styles.iconContainer}>
          <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <Path
              d="M12 5.69L17 10.19V18H15V12H9V18H7V10.19L12 5.69ZM12 3L2 12H5V20H11V14H13V20H19V12H22L12 3Z"
              fill={selectedValue === 'home' ? "#666666" : "#9E9E9E"}
            />
          </Svg>
        </View>
        <Text
          style={[
            styles.text,
            selectedValue === 'home' ? styles.activeText : styles.inactiveText,
          ]}
        >
          Home
        </Text>
      </TouchableOpacity>

      {/* Work Chip */}
      <TouchableOpacity
        style={[
          styles.chip,
          selectedValue === 'work' ? styles.activeChip : styles.inactiveChip,
        ]}
        onPress={() => onSelect('work')}
        activeOpacity={0.7}
      >
        <View style={styles.iconContainer}>
          <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <Path
              d="M10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V8C2 7.46957 2.21071 6.96086 2.58579 6.58579C2.96086 6.21071 3.46957 6 4 6H8V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2ZM10 4V6H14V4H10ZM4 8V19H20V8H4Z"
              fill={selectedValue === 'work' ? "#2F4EFF" : "#9E9E9E"}
            />
          </Svg>
        </View>
        <Text
          style={[
            styles.text,
            selectedValue === 'work' ? styles.activeText : styles.inactiveText,
          ]}
        >
          Work
        </Text>
      </TouchableOpacity>
    </View>
  );
};

ExactChipSelector.propTypes = {
  selectedValue: PropTypes.oneOf(['home', 'work']).isRequired,
  onSelect: PropTypes.func.isRequired,
  containerStyle: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    width: '100%',
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 100,
    flex: 1,
    marginHorizontal: 8,
    minHeight: 60,
  },
  activeChip: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#2F4EFF',
  },
  inactiveChip: {
    backgroundColor: '#F5F7FA',
    borderWidth: 0,
  },
  text: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  activeText: {
    color: '#000',
  },
  inactiveText: {
    color: '#9E9E9E',
  },
  iconContainer: {
    marginRight: 8,
  },
});

export default ExactChipSelector;
