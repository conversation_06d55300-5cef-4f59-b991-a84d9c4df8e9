import React from 'react';
import { TouchableOpacity, Image, StyleSheet } from 'react-native';
import icons from '../assets/icons';
import commonStyles from '../assets/commonStyles';

/**
 * A reusable component for toggling field visibility in profile forms
 * 
 * @param {boolean} isVisible - Whether the field is currently visible to others
 * @param {function} onToggle - Function to call when visibility is toggled
 * @param {object} style - Additional styles to apply to the component
 */
const VisibilityToggle = ({ isVisible, onToggle, style }) => {
  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={onToggle}
      activeOpacity={0.7}
    >
      <Image
        source={isVisible ? icons.openEyeProfileIcon : icons.closedEyeProfileIcon}
        style={styles.icon}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 5,
  },
  icon: {
    width: 20,
    height: 20,
  }
});

export default VisibilityToggle;
