import {
  Image,
  StyleSheet,
  TextInput,
  View,
  Platform,
  TouchableOpacity,
} from "react-native";
import React from "react";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "../utils/constants";
import commonStyles from "../assets/commonStyles";
import icons from "../assets/icons";
import colors from "../assets/colors";

const SearchBar = ({
  placeholder,
  onChangeText,
  value,
  onSubmitEditing,
  containerStyle,
  isRTL,
  onPressRightIcon1,
  onPressRightIcon2,
}) => {
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        gap: 8,
        marginTop: 20,
      }}
    >
      <View
        style={[
          styles.container,
          containerStyle,
          {
            flexDirection: isRTL ? "row-reverse" : "row",
            // Dynamically set width based on icon presence
            width:
              !onPressRightIcon1 && !onPressRightIcon2
                ? "100%"
                : onPressRightIcon1 && onPressRightIcon2
                ? "70%"
                : "85%",
          },
        ]}
      >
        <Image
          source={icons.SearchIcon}
          style={[commonStyles.extraSmallIcon, { tintColor: colors.black }]}
        />
        <TextInput
          placeholder={placeholder}
          placeholderTextColor={colors?.black}
          onChangeText={onChangeText}
          value={value}
          onSubmitEditing={onSubmitEditing}
          maxLength={30}
          style={[
            styles.input,
            commonStyles?.text,
            { marginLeft: 10, textAlignVertical: "center" },
          ]}
        />
      </View>
      {onPressRightIcon1 && (
        <TouchableOpacity onPress={onPressRightIcon1}>
          <Image source={icons?.filterIcon} style={{ height: 50, width: 50 }} />
        </TouchableOpacity>
      )}
      {onPressRightIcon2 && (
        <TouchableOpacity onPress={onPressRightIcon2}>
          <Image source={icons?.sortIcon} style={{ height: 50, width: 50 }} />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default SearchBar;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors?.white,

    paddingLeft: 12,
    borderRadius: 10,
    height: 50,
    width: "70%",
  },

  input: {
    flex: 1,
  },
});
