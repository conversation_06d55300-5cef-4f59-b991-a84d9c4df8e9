import React from "react";
import { View, TouchableOpacity, StyleSheet, Platform } from "react-native";
import PropTypes from "prop-types";
import colors from "../assets/colors";
import MyText from "./MyText";

/**
 * ChipSelector - A customizable chip/tab selector component
 *
 * @param {Array} options - Array of option objects with label and icon properties
 * @param {string} selectedValue - Currently selected value
 * @param {function} onSelect - Function called when an option is selected
 * @param {Object} containerStyle - Additional styles for the container
 * @param {Object} chipStyle - Additional styles for individual chips
 * @param {Object} activeChipStyle - Additional styles for the active chip
 * @param {Object} textStyle - Additional styles for the text
 * @param {Object} activeTextStyle - Additional styles for the active text
 */
const ChipSelector = ({
  options,
  selectedValue,
  onSelect,
  containerStyle,
  chipStyle,
  activeChipStyle,
  textStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      {options.map((option) => {
        const isActive = option.value === selectedValue;

        return (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.chip,
              chipStyle,
              isActive ? styles.activeChip : styles.inactiveChip,
              isActive ? activeChipStyle : null,
            ]}
            onPress={() => onSelect(option.value)}
            activeOpacity={0.7}
          >
            {option.icon && (
              <View style={styles.iconContainer}>
                {React.cloneElement(option.icon, {
                  style: [
                    styles.icon,
                    { tintColor: isActive ? colors.white : colors.black },
                  ],
                })}
              </View>
            )}
            <MyText
              p
              medium={isActive}
              regular={!isActive}
              color={isActive ? colors.white : colors.black}
              style={[textStyle]}
            >
              {option.label}
            </MyText>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

ChipSelector.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
      icon: PropTypes.element,
    })
  ).isRequired,
  selectedValue: PropTypes.string.isRequired,
  onSelect: PropTypes.func.isRequired,
  containerStyle: PropTypes.object,
  chipStyle: PropTypes.object,
  activeChipStyle: PropTypes.object,
  textStyle: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "left",
    alignItems: "center",
    marginVertical: 10,
  },
  chip: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 10,
    padding: 8,
    paddingHorizontal: 8,
  },
  activeChip: {
    backgroundColor: colors.primary,
    borderWidth: 1,
    borderColor: colors.primary,
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  inactiveChip: {
    backgroundColor: colors.gray,
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
      },
      android: {
        elevation: 3,
      },
    }),
    // borderWidth: 0.2,
    // borderColor: colors.primaryAlpha(0.06),
  },
  activeText: {
    color: colors.white,
  },
  inactiveText: {
    color: colors.black,
  },
  iconContainer: {
    marginRight: 8,
  },
  icon: {
    height: 13,
    width: 13,
    resizeMode: "contain",
  },
});

export default ChipSelector;
