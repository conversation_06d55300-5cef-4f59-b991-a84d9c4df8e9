import React, { use, useState } from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import { DrawerContentScrollView } from "@react-navigation/drawer";
import icons from "../assets/icons";
import colors from "../assets/colors";
import commonStyles from "../assets/commonStyles";
import { useDispatch, useSelector } from "react-redux";
import AppModal from "./AppModal";
import { logout, logoutUser } from "../redux/features/authSlice";
import { CommonActions } from "@react-navigation/native";
import Avatar from "./Avatar";
import { GoogleSignin } from "@react-native-google-signin/google-signin";

export default function CustomDrawerContent({ navigation, props }) {
  const dispatch = useDispatch();
  const [modalVisibleForLogin, setModalVisibleForLogin] = useState({
    visible: false,
    title: "",
    description: "",
    singleButton: true,
    secondButtonText: "",
    firstButtonText: "",
    onClose: () =>
      setModalVisibleForLogin((prev) => ({ ...prev, visible: false })),
    onSubmit: null,
  });

  const user = useSelector((state) => state.auth.user);
  const token = useSelector((state) => state.auth.token);

  const handleLogout = () => {
    navigation.toggleDrawer();
    setModalVisibleForLogin({
      visible: true,
      title: "Logout",
      description: "Are you sure you want to logout?",
      btnTitle: "Logout",
      secondaryBtnTitle: "Cancel",
      onClose: () =>
        setModalVisibleForLogin((prev) => ({ ...prev, visible: false })),
      onSubmit: async () => {
        try {
          await GoogleSignin.signOut();
          const payload = {
            token: token,
          };

          console.log("Logout payload:", payload);

          const response = await dispatch(logout(payload));
          console.log("Logout response:", JSON.stringify(response));
          if (response.payload.success) {
            console.log("Logout successful");
          } else {
            console.log("Logout failed");
          }
        } catch (error) {
          console.error("Logout error:", error);
        }
        // return;
        dispatch(logoutUser());
        setModalVisibleForLogin((prev) => ({ ...prev, visible: false }));
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "Onboarding" }],
          })
        );
      },
    });
  };

  return (
    <DrawerContentScrollView contentContainerStyle={styles.container}>
      <View style={styles.header}>
        <Image source={icons.AppLogo} resizeMode="cover" style={styles.logo} />
        <TouchableOpacity onPress={() => navigation.closeDrawer()}>
          <Image source={icons.closeIcon} style={styles.closeIcon} />
        </TouchableOpacity>
      </View>

      {/* Profile Info */}
      <View
        style={[commonStyles.rowWithoutSpaceBetween, styles.profileSection]}
      >
        <Avatar url={user?.profile_image} name={user?.firstName} size={55} />
        <Text style={styles.profileText}>
          Hey {user?.firstName + " " + user?.lastName}!
        </Text>
        
      </View>

      {/* Menu Items */}
      <View style={styles.menu}>
        <MenuItem icon={icons.subscriptionIcon} label="Subscriptions" />
        <MenuItem
          icon={icons.settingsIcon}
          label="Settings"
          onPress={() => navigation.navigate("SettingsScreen")}
        />
        <MenuItem
          icon={icons.termsConditionsIcon}
          label="Terms & Conditions"
          onPress={() => navigation.navigate("TermsConditionsScreen")}
        />
        <MenuItem
          icon={icons.privacyPolicyIcon}
          label="Privacy Policy"
          onPress={() => navigation.navigate("PrivacyPolicyScreen")}
        />
        <MenuItem
          icon={icons.AboutUsIcon}
          label="About Us"
          onPress={() => navigation.navigate("AboutUsScreen")}
        />
        <MenuItem
          icon={icons.contactUsIcon}
          label="Contact Us"
          onPress={() => navigation.navigate("ContactUsScreen")}
        />
        {/* <MenuItem icon={icons.helpIcon} label="Help" /> */}
      </View>

      {/* Logout */}
      <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
        <Image source={icons.logoutIcon} style={styles.logoutIcon} />
        <Text style={styles.logoutText}>Log Out</Text>
      </TouchableOpacity>
      <AppModal {...modalVisibleForLogin} />
    </DrawerContentScrollView>
  );
}

function MenuItem({ icon, label, onPress }) {
  return (
    <TouchableOpacity onPress={onPress} style={styles.menuItem}>
      <Image source={icon} style={styles.menuIcon} />
      <Text style={styles.menuLabel}>{label}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  logo: {
    height: 40,
    width: 120,
    resizeMode: "contain",
  },
  closeIcon: {
    height: 18,
    width: 18,
    ...commonStyles.smallIcon,
    //   tintColor: '#000',
  },
  profileSection: {
    marginTop: 30,
    alignItems: "center",
    justifyContent: "flex-start",
  },
  profileImage: {
    height: 45,
    width: 45,
    borderRadius: 22.5,
    marginBottom: 8,
    // backgroundColor: "red",
    alignSelf: "center",
  },
  profileText: {
    fontSize: 15,
    fontWeight: "600",
  },
  menu: {
    marginTop: 30,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
  },
  menuIcon: {
    height: 20,
    width: 20,
    marginRight: 12,
    resizeMode: "contain",
    tintColor: colors.primary, // Optional
  },
  menuLabel: {
    fontSize: 15,
    color: "#000",
  },
  logoutButton: {
    marginTop: 40,
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 10,
    backgroundColor: "#FFE5E5",
    alignSelf: "flex-start",
    bottom: 80,
    left: 20,
    position: "absolute",
  },
  logoutIcon: {
    height: 16,
    width: 16,
    marginRight: 8,
    resizeMode: "contain",
    tintColor: "#FF4D4D",
  },
  logoutText: {
    color: "#FF4D4D",
    fontWeight: "600",
  },
});
