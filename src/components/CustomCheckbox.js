import React from "react";
import { TouchableOpacity, StyleSheet, Text } from "react-native";
import colors from "../assets/colors";

const CustomCheckbox = ({ checked, onChange }) => {
  return (
    <TouchableOpacity
      onPress={onChange}
      style={[styles.box, checked ? styles.boxChecked : styles.boxUnchecked]}
    >
      {checked && <Text style={styles.checkmark}>✓</Text>}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  box: {
    width: 20,
    height: 20,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: colors.inputBg,
  },
  boxChecked: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  boxUnchecked: {
    borderWidth: 2,
    borderColor: colors.inputBg,
  },
  checkmark: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: "bold",
    textAlign: "center",
    textAlignVertical: "center",
  },
});

export default CustomCheckbox;
