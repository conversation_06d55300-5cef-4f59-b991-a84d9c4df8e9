import React, { useEffect } from "react";
import { View, Modal, StyleSheet, Animated } from "react-native";
import colors from "../assets/colors";
import { PrimaryButton } from "./Button";
import MyText from "./MyText";
import commonStyles from "../assets/commonStyles";

export default function AppModal({
  visible,
  title,
  description,
  onClose,
  onSubmit,
  btnTitle,
  secondaryBtnTitle,
}) {
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={onClose} // Handle back button on Android
    >
      <View style={styles.modalContainer}>
        <View style={styles.container}>
          <MyText h5 semibold children={title ?? "Title"} />
          <View style={styles.descriptionContainer}>
            <MyText p regular center children={description ?? "Description"} />
          </View>
          <PrimaryButton
            onPress={onSubmit}
            title={btnTitle ?? "Ok"}
            style={[styles.btnStyle]}
          />
          <MyText
            p
            thin
            children={secondaryBtnTitle ?? "May be another time"}
            style={styles.closeText}
            onPress={onClose}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 100,
  },
  container: {
    width: "85%",
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 100,
  },
  descriptionContainer: {
    paddingHorizontal: 14,
    paddingVertical: 16,
  },
  btnStyle: {
    width: "50%",
    marginVertical: 10,
  },
  closeText: {
    marginVertical: 10,
    color: colors.black,
    textDecorationLine: "underline",
  },
});
