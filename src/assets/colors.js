const colors = {
  primary: "#2F4EFF",
  primaryAlpha: (alpha) => {
    return `rgba(47,78,255,${alpha})`;
  },
  inputBg: "rgba(240, 240, 240, 1)",
  chipBg: "rgba(245, 245, 245, 1)",
  black: "black",
  white: "white",
  gray: "#E4E7EB",
  gray1: "#BBBBBB",
  txtGray: "#978D8D",
  blue: "#5BBCFF",
  red: "#E72929",
  secondary: "#EEF2FF",
  errorRed: "rgb(193, 80, 80)",
  gradient: ["#2F4EFF", "#2F4EFF", "#7187FF"],
};
export default colors;
