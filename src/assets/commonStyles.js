import { Platform, StyleSheet } from "react-native";
import colors from "./colors";

export default commonStyles = StyleSheet.create({
  text: {
    fontSize: 13,
    color: colors.black,
    fontFamily: "Metropolis-Regular",
  },
  textWhite: {
    fontSize: 13,
    color: colors.white,
    fontFamily: "Metropolis-Regular",
  },
  titleText: {
    color: colors.black,
    fontWeight: "500",
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  submitButtonText: {
    color: colors.white,
  },
  center: {
    alignItems: "center",
    justifyContent: "center",
  },
  appLogo: {
    height: 150,
    width: 150,
  },
  midIcon: {
    height: 32,
    width: 32,
  },
  smallIcon: {
    height: 24,
    width: 24,
  },
  extraSmallIcon: {
    height: 10,
    width: 10,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  rowWithoutSpaceBetween: {
    flexDirection: "row",
    // justifyContent: "space-between"
    gap: 10,
  },
  header: {
    midIcon: {
      height: 44,
      width: 44,
    },
    smallIcon: {
      height: 41,
      width: 41,
    },
  },
  shadow: {
    ...Platform.select({
      ios: {
        shadowColor: colors.black, // Shadow color
        shadowOffset: { width: 0, height: 1 }, // Offset for the shadow
        shadowOpacity: 0.4, // Opacity of the shadow
        shadowRadius: 1.4, // Blur radius for the shadow
      },
      android: {
        elevation: 3, // Android uses elevation to create shadow
      },
    }),
  },
});
