import React, { useState } from 'react';
import { View, Alert } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { updateProfile } from '../redux/features/SharingProfileSlice';
import { PrimaryButton } from '../components/Button';

const ProfileUpdateExample = ({ profile_management_id }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  
  // Get the update profile state from the redux store
  const updateProfileState = useSelector(
    (state) => state.sharingProfileSlice.updateProfile
  );

  const handleUpdateProfile = async () => {
    try {
      setLoading(true);
      
      // Sample profile data structure based on the provided JSON
      const profileData = {
        userInfo: {
          emails: [
            {
              address: "<EMAIL>",
              type: "home"
            },
            {
              address: "<EMAIL>",
              type: "home"
            }
          ],
          phoneNumbers: [
            {
              number: "5551234567",
              countryCode: "+1",
              type: "mobile"
            },
            {
              number: "5551234567",
              countryCode: "+1",
              type: "mobile"
            }
          ],
          company_logo: "company_logo.jpg",
          source: "google",
          sourceId: "google-contact-id-123",
          spouseName: "<PERSON>",
          nickname: "Janie",
          dateOfBirth: "1990-05-15",
          gender: "female",
          nationality: "American",
          maritalStatus: "married",
          languages: ["English", "Spanish"],
          hobbies: ["Reading", "Hiking"],
          addresses_home: {
            apartment: "Apt 42",
            street: "123 Main St",
            city: "New York",
            state: "NY",
            postalCode: "10001",
            country: "USA"
          },
          addresses_other: {
            apartment: "Suite 100",
            street: "456 Business Ave",
            city: "New York",
            state: "NY",
            postalCode: "10002",
            country: "USA"
          },
          // Add other user info fields as needed
        },
        getProfileAccess: {
          emails_boolean: [
            {
              address_boolean: false,
              type_boolean: false
            },
            {
              address_boolean: false,
              type_boolean: false
            }
          ],
          phoneNumbers_boolean: [
            {
              number_boolean: false,
              countryCode_boolean: false,
              type_boolean: false
            },
            {
              number_boolean: false,
              countryCode_boolean: false,
              type_boolean: false
            }
          ],
          profile_image_boolean: false,
          firstName_boolean: false,
          lastName_boolean: true,
          nickname_boolean: true,
          dateOfBirth_boolean: true,
          gender_boolean: true,
          nationality_boolean: true,
          maritalStatus_boolean: true,
          hobbies_boolean: true,
          // Add other boolean fields as needed
        }
      };

      // Dispatch the updateProfile action with the profile_management_id and profileData
      const response = await dispatch(
        updateProfile({ profile_management_id, profileData })
      );

      if (response.payload && response.payload.success) {
        Alert.alert('Success', 'Profile updated successfully');
      } else {
        Alert.alert('Error', 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ padding: 20 }}>
      <PrimaryButton
        title="Update Profile"
        onPress={handleUpdateProfile}
        loading={loading || updateProfileState.loading}
      />
    </View>
  );
};

export default ProfileUpdateExample;
