import React, { useState } from "react";
import { View, Alert } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { updateSharingProfile } from "../redux/features/SharingProfileSlice";
import { PrimaryButton } from "../components/Button";
import { mapProfileDataForApi } from "../utils/profileDataMapper";

/**
 * Example component demonstrating how to map profile data and send it to the updateSharingProfile API
 *
 * @param {Object} props - Component props
 * @param {string} props.profile_management_id - The profile management ID
 * @param {Object} props.profileData - The profile data containing userInfo and getProfileAccess
 * @returns {JSX.Element} - The rendered component
 */
const UpdateSharingProfileExample = ({
  profile_management_id,
  profileData,
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  // Get the update profile state from the redux store
  const updateSharingProfileState = useSelector(
    (state) => state.sharingProfileSlice.updateSharingProfile
  );

  /**
   * Handles the update profile action
   */
  const handleUpdateProfile = async () => {
    try {
      setLoading(true);

      // Map the profile data to the format required by the API
      const mappedData = mapProfileDataForApi(profileData);

      // Dispatch the updateSharingProfile action with the profile_management_id and mapped data
      const response = await dispatch(
        updateSharingProfile({
          profile_management_id, // Pass as a separate parameter
          profileData: mappedData, // Pass the mapped data as profileData
        })
      );

      if (response.payload && response.payload.success) {
        Alert.alert("Success", "Profile updated successfully");
      } else {
        Alert.alert("Error", "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ padding: 20 }}>
      <PrimaryButton
        title="Update Sharing Profile"
        onPress={handleUpdateProfile}
        loading={loading || updateSharingProfileState.loading}
      />
    </View>
  );
};

export default UpdateSharingProfileExample;
