import React from "react";
import { View, Text, StyleSheet } from "react-native";
import UpdateSharingProfileExample from "./UpdateSharingProfileExample";

/**
 * Example component demonstrating how to use the UpdateSharingProfileExample component
 *
 * @returns {JSX.Element} - The rendered component
 */
const UsageExample = () => {
  // Example profile management ID (this would typically come from your app state)
  const profile_management_id = "example-profile-id";

  // Example profile data (this would typically come from an API response or state)
  const profileData = {
    userInfo: {
      // firstName: "Jane",
      // lastName: "Smith",
      emails: [
        {
          address: "<EMAIL>",
          type: "home",
        },
        {
          address: "<EMAIL>",
          type: "home",
        },
      ],
      phoneNumbers: [
        {
          number: "5551234567",
          countryCode: "+1",
          type: "mobile",
        },
        {
          number: "5551234567",
          countryCode: "+1",
          type: "mobile",
        },
      ],
      // email: "<EMAIL>",
      // country_code: "+1",
      // phone_number: "+1987654321",
      company_logo: "company_logo.jpg",
      source: "google",
      sourceId: "google-contact-id-123",
      spouseName: "<PERSON>",
      nickname: "Janie",
      dateOfBirth: "1990-05-15",
      gender: "female",
      nationality: "American",
      maritalStatus: "married",
      languages: ["English", "Spanish"],
      hobbies: ["Reading", "Hiking"],
      addresses_home: {
        apartment: "Apt 42",
        street: "123 Main St",
        city: "New York",
        state: "NY",
        postalCode: "10001",
        country: "USA",
      },
      addresses_other: {
        apartment: "Suite 100",
        street: "456 Business Ave",
        city: "New York",
        state: "NY",
        postalCode: "10002",
        country: "USA",
      },
      socialMedia: {
        facebook: {
          url: "https://facebook.com/john.doe.profile",
        },
        instagram: {
          url: "https://instagram.com/john.doe",
        },
        twitter: {
          url: "https://twitter.com/johndoe",
        },
        linkedin: {
          url: "https://linkedin.com/in/john-doe",
        },
        snapchat: {
          url: "https://snapchat.com/add/johndoe",
        },
        whatsapp: {
          url: "https://wa.me/**********",
        },
        telegram: {
          url: "https://t.me/johndoe",
        },
        signal: {
          url: "https://signal.me/#p/+**********",
        },
        skype: {
          url: "https://join.skype.com/invite/johndoe",
        },
        youtube: {
          url: "https://youtube.com/johndoechannel",
        },
        twitch: {
          url: "https://twitch.tv/johndoe",
        },
        tiktok: {
          url: "https://tiktok.com/@johndoe",
        },
        iMessage: {
          url: "imessage:<EMAIL>",
        },
        googleChat: {
          url: "https://chat.google.com/johndoe",
        },
        discord: {
          url: "https://discord.com/users/johndoe#1234",
        },
        slack: {
          url: "https://workspace.slack.com/team/johndoe",
        },
        wechat: {
          url: "weixin://dl/chat?johndoe",
        },
        kik: {
          url: "kik://username/johndoe",
        },
        line: {
          url: "https://line.me/ti/p/~johndoe",
        },
      },
      emergency_contact: {
        contactName: "John Smith",
        phoneNumber_country_code: "+1",
        phoneNumber: "+**********",
        relationship: "spouse",
      },
      healthInsurance: {
        policyNumber: "HI-123456",
        policyPeriod: 12,
        effectiveDate: "2023-01-01",
        expirationDate: "2023-12-31",
        sumInsured: 500000,
      },
      billing_address: {
        officeBuilding: "Tower 1",
        street: "789 Finance Blvd",
        city: "New York",
        state: "NY",
        postalCode: "10003",
        country: "USA",
      },
      account_details: {
        name: "Janeeqweq Smith",
        bank: "Chase",
        accountNumber: "**********",
        ifscCode: "CHASUS33",
        paypalEmail: "<EMAIL>",
      },
      card_details: {
        nameOnCard: "JANE SMITH",
        cardNumber: "****************",
        expiryDate: "12/25",
        cvv: "123",
      },
    },
    getProfileAccess: {
      emails_boolean: [
        {
          address_boolean: false,
          type_boolean: false,
        },
        {
          address_boolean: false,
          type_boolean: false,
        },
      ],
      phoneNumbers_boolean: [
        {
          number_boolean: false,
          countryCode_boolean: false,
          type_boolean: false,
        },
        {
          number_boolean: false,
          countryCode_boolean: false,
          type_boolean: false,
        },
      ],
      // email_boolean: true,
      // country_code_boolean: false,
      // phone_number_boolean: false,
      profile_image_boolean: false,
      firstName_boolean: false,
      lastName_boolean: true,
      nickname_boolean: true,
      dateOfBirth_boolean: true,
      gender_boolean: true,
      nationality_boolean: true,
      maritalStatus_boolean: true,
      hobbies_boolean: true,
      addresses_home_boolean: {
        apartment_boolean: true,
        street_boolean: true,
        city_boolean: true,
        state_boolean: true,
        postalCode_boolean: true,
        country_boolean: false,
      },
      addresses_other_boolean: {
        apartment_boolean: false,
        street_boolean: false,
        city_boolean: true,
        state_boolean: true,
        postalCode_boolean: true,
        country_boolean: true,
      },
      company_logo_boolean: false,
      timeZone_boolean: true,
      preferredContactMethod_boolean: true,
      personalWebsite_boolean: true,
      religion_boolean: true,
      socialMedia_boolean: {
        facebook_boolean: true,
        instagram_boolean: true,
        twitter_boolean: true,
        linkedin_boolean: true,
        snapchat_boolean: true,
        whatsapp_boolean: true,
        telegram_boolean: true,
        signal_boolean: true,
        skype_boolean: true,
        youtube_boolean: true,
        twitch_boolean: true,
        tiktok_boolean: true,
        iMessage_boolean: true,
        googleChat_boolean: true,
        discord_boolean: true,
        slack_boolean: true,
        wechat_boolean: true,
        kik_boolean: true,
        line_boolean: true,
      },
      emergency_contact: {
        contactName_boolean: true,
        phoneNumber_boolean: true,
        mobileNumber_boolean: true,
        relationship_boolean: true,
      },
      healthInsurance: {
        policyNumber_boolean: true,
        policyPeriod_boolean: true,
        effectiveDate_boolean: true,
        expirationDate_boolean: true,
        sumInsured_boolean: true,
      },
      billing_address_boolean: {
        officeBuilding_boolean: false,
        street_boolean: false,
        city_boolean: false,
        state_boolean: false,
        postalCode_boolean: false,
        country_boolean: false,
      },
      account_details_boolean: {
        name_boolean: true,
        bank_boolean: false,
        accountNumber_boolean: false,
        ifscCode_boolean: false,
        paypalEmail_boolean: false,
      },
      card_details_boolean: {
        nameOnCard_boolean: false,
        cardNumber_boolean: false,
        expiryDate_boolean: false,
        cvv_boolean: false,
      },
    },
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Update Sharing Profile Example</Text>
      <Text style={styles.description}>
        This example demonstrates how to map profile data and send it to the
        updateSharingProfile API.
      </Text>
      <UpdateSharingProfileExample
        profile_management_id={profile_management_id}
        profileData={profileData}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#fff",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    marginBottom: 20,
    color: "#666",
  },
});

export default UsageExample;
