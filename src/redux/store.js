import {
  combineReducers,
  configureStore,
  getDefaultMiddleware,
} from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
// import storage from 'react-native-mmkv';
import authReducer from "./features/authSlice";
import contactReducer from "./features/contactSlice"; // Import the contactSlice reducer
import mainReducer from "./features/mainSlice";
import sharingProfileReducer from "./features/SharingProfileSlice"; // Import the sharingProfileSlice reducer
import { MMKVLoader } from "react-native-mmkv-storage";

const storage = new MMKVLoader().initialize();

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["auth", "mainSlice"], // Specify which reducers to persist
};

const rootReducer = combineReducers({
  auth: authReducer,
  contactSlice: contactReducer, // Add the contactSlice reducer here
  mainSlice: mainReducer,
  sharingProfileSlice: sharingProfileReducer, // Add the sharingProfileSlice reducer here
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

export default store;
