import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { api, multipartApi } from "../../axios/axiosConfig";
import { set } from "react-hook-form";

export const getFAQS = createAsyncThunk("auth/getFAQS", async (payload) => {
  try {
    const response = await api.get("/faq/list", payload);
    return response.data;
  } catch (error) {
    return error.response;
  }
});

export const getAboutUs = createAsyncThunk(
  "auth/getAboutUs",
  async (payload) => {
    try {
      const response = await api.get("/receive/about_us", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const getPrivacyPolicy = createAsyncThunk(
  "auth/getPrivacyPolicy",
  async (payload) => {
    try {
      const response = await api.get("/receive/privacy_policy", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);
export const getTermsAndConditions = createAsyncThunk(
  "auth/getTermsAndConditions",
  async (payload) => {
    try {
      const response = await api.get("/receive/terms_conditions", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const GenerateImageLink = createAsyncThunk(
  "auth/uploadImage",
  async (payload) => {
    let attempts = 0;
    const maxRetries = 3;

    while (attempts < maxRetries) {
      try {
        const response = await multipartApi.post(
          "/edit/profile/image",
          payload
        );
        return response.data;
      } catch (error) {
        attempts += 1;
        console.error(`Attempt ${attempts} failed:`, error.message);

        if (attempts >= maxRetries) {
          console.error("Max retries reached. Returning error.");
          return {
            success: false,
            message: "Upload failed after multiple attempts",
          };
        }

        // Adding a delay before the next retry (exponential backoff)
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * Math.pow(2, attempts))
        );
      }
    }
  }
);

export const editProfile = createAsyncThunk(
  "auth/editProfile",
  async (payload) => {
    try {
      const response = await api.post("/edit-account", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);
export const deleteAccount = createAsyncThunk(
  "auth/deleteAccount",
  async (payload) => {
    try {
      const response = await api.delete("/delete-account", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const getUserChat = createAsyncThunk(
  "auth/getUserChat",
  async (payload) => {
    try {
      const response = await api.get("/chat", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const changePassword = createAsyncThunk(
  "auth/changePassword",
  async (payload) => {
    try {
      const response = await api.post("/change-password", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const updatePermission = createAsyncThunk(
  "auth/updatePermission",
  async (payload) => {
    try {
      const response = await api.put("/toggle", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const createTag = createAsyncThunk("auth/createTag", async (payload) => {
  try {
    const response = await api.post("/tag", payload);
    return response.data;
  } catch (error) {
    return error.response;
  }
});

export const getTags = createAsyncThunk("auth/getTags", async (payload) => {
  try {
    const params = payload || { page: 1, limit: 100 };
    const response = await api.get("/tags", { params });
    return response.data;
  } catch (error) {
    return error.response;
  }
});

export const getTagByID = createAsyncThunk(
  "auth/getTagByID",
  async (payload) => {
    try {
      const response = await api.get(`/tags/${payload}`);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const editTag = createAsyncThunk("auth/editTag", async (payload) => {
  try {
    const response = await api.put(`/user-tags/${payload.id}`, payload.data);
    return response.data;
  } catch (error) {
    return error.response;
  }
});

export const deleteTag = createAsyncThunk(
  "auth/deleteTag",
  async (payload) => {
    try {
      const response = await api.delete(`/tags/${payload}`);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const personalProfile = createAsyncThunk(
  "auth/personalProfile",
  async () => {
    try {
      const response = await api.get(`/all-personal-profile-details`);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

const initialState = {
  tagsData: {
    contacts: [],
    tagName: "",
    tags: [], // <-- add this to store all tags
  },
  profileContactsData: [], // <-- add this for profile contacts
  selectedContactsForProfile: [], // <-- add this for selected contacts
  showModal: "",
  selectedLayout: "layout1",
  preSetEmail: "",
  preSetPassword: "",
};

const mainSlice = createSlice({
  name: "main",
  initialState,
  reducers: {
    setTagsData: (state, action) => {
      state.tagsData = { ...state.tagsData, ...action.payload };
    },
    setProfileContactsData: (state, action) => {
      state.profileContactsData = action.payload;
    },
    setSelectedContactsForProfile: (state, action) => {
      state.selectedContactsForProfile = action.payload;
    },
    setShowModal: (state, action) => {
      state.showModal = action.payload;
    },
    setSelectedLayout: (state, action) => {
      state.selectedLayout = action.payload;
    },
    setPreSetEmail: (state, action) => {
      state.preSetEmail = action.payload;
    },
    setPreSetPassword: (state, action) => {
      state.preSetPassword = action.payload;
    },
    resetPreSetEmail: (state) => {
      state.preSetEmail = "";
    },
    resetPreSetPassword: (state) => {
      state.preSetPassword = "";
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getTags.fulfilled, (state, action) => {
      // Store tags array in tagsData.tags for HomeScreen
      if (
        action.payload &&
        action.payload.data &&
        Array.isArray(action.payload.data.result)
      ) {
        state.tagsData.tags = action.payload.data.result;
      } else {
        state.tagsData.tags = [];
      }
    });
  },
});
export const {
  setTagsData,
  setProfileContactsData,
  setSelectedContactsForProfile,
  setShowModal,
  setSelectedLayout,
  setPreSetEmail,
  setPreSetPassword,
  resetPreSetEmail,
  resetPreSetPassword,
} = mainSlice.actions;
export default mainSlice.reducer;
