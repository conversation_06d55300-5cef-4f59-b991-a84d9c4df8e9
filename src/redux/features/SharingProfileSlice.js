import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { api } from "../../axios/axiosConfig";

// Async thunk to call the personal-profile API
export const getPersonalProfile = createAsyncThunk(
  "personal-profile",
  async (payload) => {
    try {
      const response = await api.get("/personal-profile", payload);
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

// Async thunk to update profile with the provided JSON data
export const updateSharingProfile = createAsyncThunk(
  "update-profile",
  async ({ profile_management_id, profileData }) => {
    try {
      const response = await api.post(
        `/setup-profile/${profile_management_id}`,
        profileData
      );
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);
// Async thunk to update profile with the provided JSON data
export const updateBusinessDetailsSharingProfile = createAsyncThunk(
  "update-business-details",
  async ({ profile_management_id, profileData }) => {
    try {
      const response = await api.post(
        `/business-details/${profile_management_id}`,
        profileData
      );
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

// Async thunk to call the new get API
export const getProfileNameData = createAsyncThunk(
  "/all-profile-name",
  async (payload) => {
    try {
      const response = await api.get("/all-profile-name", payload);
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

// Async thunk to store a new profile
export const storeProfile = createAsyncThunk("store-profile", async (body) => {
  try {
    const response = await api.post("/store-profile", body);
    return response?.data;
  } catch (error) {
    return error.response?.data || { message: error.message };
  }
});

// Async thunk to get profile members by profile id
export const getProfileMembersByProfileId = createAsyncThunk(
  "get-profile-members-by-profile-id",
  async (profile_management_id) => {
    try {
      const response = await api.get(
        `/profile-members/${profile_management_id}`
      );
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);
export const addContactsToProfile = createAsyncThunk(
  "add-contacts-to-profile",
  async (payload) => {
    console.log("🚀 ~ payload:", JSON.stringify(payload, null, 2));
    try {
      const response = await api.post(
        `user-profile/${payload.profile_management_id}`,
        payload.body
      );
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

// Async thunk to get rest profiles by profile id
export const getRestProfilesByProfileId = createAsyncThunk(
  "get-rest-profiles-by-profile-id",
  async (profile_management_id) => {
    try {
      const response = await api.get(`profile/${profile_management_id}`);
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);
// Async thunk to move or remove member from profile
export const moveOrRemoveMemberFromProfile = createAsyncThunk(
  "move-or-remove-member-from-profile",
  async (body) => {
    try {
      const response = await api.put(`move-profile`, body);
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);
// Async thunk to get access profile from profile id
export const getAccessProfileFromProfileId = createAsyncThunk(
  "get-access-profile-from-profile-id",
  async (profileId) => {
    try {
      const response = await api.get(`profile/access/${profileId}`);
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

const createAsyncReducers = (builder, asyncThunk, stateKey) => {
  builder
    .addCase(asyncThunk.pending, (state) => {
      state[stateKey] = { loading: true, error: null, data: null };
    })
    .addCase(asyncThunk.fulfilled, (state, action) => {
      state[stateKey] = { loading: false, error: null, data: action.payload };
    })
    .addCase(asyncThunk.rejected, (state, action) => {
      state[stateKey] = { loading: false, error: action.error, data: null };
    });
};

const sharingProfileSlice = createSlice({
  name: "sharingProfileSlice",
  initialState: {
    getPersonalProfile: { loading: false, error: null, data: null },
    updateSharingProfile: { loading: false, error: null, data: null },
    getProfileNameData: { loading: false, error: null, data: null },
    storeProfile: { loading: false, error: null, data: null },
    getProfileMembersByProfileId: { loading: false, error: null, data: null },
    getRestProfilesByProfileId: { loading: false, error: null, data: null },
    moveOrRemoveMemberFromProfile: { loading: false, error: null, data: null },
    addContactsToProfile: { loading: false, error: null, data: null },
    getAccessProfileFromProfileId: { loading: false, error: null, data: null },
    updateBusinessDetailsSharingProfile: {
      loading: false,
      error: null,
      data: null,
    },
  },
  reducers: {},
  extraReducers: (builder) => {
    createAsyncReducers(builder, getPersonalProfile, "getPersonalProfile");
    createAsyncReducers(builder, updateSharingProfile, "updateSharingProfile");
    createAsyncReducers(builder, getProfileNameData, "getProfileNameData");
    createAsyncReducers(builder, storeProfile, "storeProfile");
    createAsyncReducers(
      builder,
      getProfileMembersByProfileId,
      "getProfileMembersByProfileId"
    );
    createAsyncReducers(
      builder,
      getRestProfilesByProfileId,
      "getRestProfilesByProfileId"
    );
    createAsyncReducers(
      builder,
      moveOrRemoveMemberFromProfile,
      "moveOrRemoveMemberFromProfile"
    );
    createAsyncReducers(builder, addContactsToProfile, "addContactsToProfile");
    createAsyncReducers(
      builder,
      getAccessProfileFromProfileId,
      "getAccessProfileFromProfileId"
    );
    createAsyncReducers(
      builder,
      updateBusinessDetailsSharingProfile,
      "updateBusinessDetailsSharingProfile"
    );
  },
});

export default sharingProfileSlice.reducer;
