import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { Alert } from "react-native";
import { storage } from "../../utils/storage";
import { api } from "../../axios/axiosConfig";

export const sendOtp = createAsyncThunk("auth/sendOtp", async (payload) => {
  try {
    const response = await api.post("/user/send-otp", payload);
    return response;
  } catch (error) {
    return error;
  }
});

export const signup = createAsyncThunk("auth/signup", async (payload) => {
  try {
    const response = await api.post("/register", payload);
    return response.data;
  } catch (error) {
    // Return error.response to match the pattern used in other actions
    return error.response ? error.response : error;
  }
});

export const login = createAsyncThunk("auth/login", async (payload) => {
  try {
    const response = await api.post("/login", payload);
    return response.data;
  } catch (error) {
    return error.response;
  }
});

export const socialSignup = createAsyncThunk(
  "auth/socialSignup",
  async (payload) => {
    try {
      const response = await api.post("/social-signup", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);
export const socialLogin = createAsyncThunk(
  "auth/socialLogin",
  async (payload) => {
    try {
      const response = await api.post("/auth/social-login", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const verifySocialSignupOtp = createAsyncThunk(
  "auth/verifySocialSignupOtp",
  async (payload) => {
    try {
      const response = await api.post("/verify-social-signup-otp", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const verifySignupOtp = createAsyncThunk(
  "auth/verifySignupOtp",
  async (payload) => {
    try {
      const response = await api.post("/verify-signup-otp", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const verifyLoginOtp = createAsyncThunk(
  "auth/verifyLoginOtp",
  async (payload) => {
    try {
      const response = await api.post("/verify-login-otp", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const forgetPasswrord = createAsyncThunk(
  "auth/forgetPassword",
  async (payload) => {
    try {
      const response = await api.post("/forget-password", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const verifyForgetPasswordOtp = createAsyncThunk(
  "auth/verifyForgetPasswordOtp",
  async (payload) => {
    try {
      const response = await api.post("/verify-forget-email-otp", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);
export const resetPassword = createAsyncThunk(
  "auth/resetPassword",
  async (payload) => {
    try {
      const response = await api.post("/reset-password", payload);
      return response.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const getProfile = createAsyncThunk(
  "auth/getProfile",
  async (payload) => {
    try {
      const response = await api.get("/own-profile-details", payload);
      // console.log("Profile response:", response.data);
      
      return response;
    } catch (error) {
      return error;
    }
  }
);

export const logout = createAsyncThunk("auth/logout", async (payload) => {
  try {
    const response = await api.post("/logout", payload);
    return response.data;
  } catch (error) {
    return error.response;
  }
});

const initialState = {
  token: null,
  user: null,
  error: false,
  loading: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setToken: (state, action) => {
      state.token = action.payload;
      // storage.set('token', action.payload);
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    logoutUser: (state, action) => {
      (state.token = null), (state.user = null);
      //   storage.delete("token");
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProfile.pending, (state) => {
        state.loading = true;
        state.error = false;
      })
      .addCase(getProfile.fulfilled, (state, action) => {
        state.loading = false;
        console.log("Profile:", action.payload.data.data);

        state.user = action.payload.data.data;
      })
      .addCase(getProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = true;
        console.log("Error fetching profile:", action.error);
      });
  },
});

export const { setToken, setUser, setError, setLoading, logoutUser } =
  authSlice.actions;

export default authSlice.reducer;
