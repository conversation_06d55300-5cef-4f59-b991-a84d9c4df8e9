import { View, Text, Image } from "react-native";
import colors from "../assets/colors";
import Toast from "react-native-toast-message";
import icons from "../assets/icons";
import commonStyles from "../assets/commonStyles";
import MyText from "../components/MyText";

const baseToastStyle = {
  backgroundColor: colors.white,
  borderRadius: 8,
  paddingVertical: 14,
  paddingHorizontal: 15,
  width: "90%",
  shadowColor: colors.black,
  shadowOpacity: 0.2,
  shadowRadius: 8,
  shadowOffset: { width: 10, height: 2 },
  alignSelf: "center",
  marginTop: 20,
};

const baseTextStyle = {
  fontSize: 14,
  color: colors.black,
};

export const toastConfig = {
  success: (props) => (
    <View
      style={{
        ...baseToastStyle,
        borderLeftColor: colors.primary,
        borderLeftWidth: 5,
        flexDirection: "row",
      }}
    >
      <MyText p style={{ ...baseTextStyle }}>
        {props.text1}
      </MyText>
      {props.text2 && (
        <Text
          style={{
            ...baseTextStyle,
            fontFamily: "Metropolis-Regular",
            marginVertical: 4,
            fontSize: 14,
          }}
        >
          {props.text2}
        </Text>
      )}
    </View>
  ),
  error: (props) => (
    <View
      style={{
        ...baseToastStyle,
        borderLeftColor: colors.red,
        borderLeftWidth: 5,
      }}
    >
      <MyText p color={colors.errorRed} style={{ ...baseTextStyle }}>
        {props.text1}
      </MyText>
      {props.text2 && (
        <Text
          style={{
            ...baseTextStyle,
            fontFamily: "Metropolis-Regular",
            color: colors.errorRed,
            marginVertical: 4,
            fontSize: 14,
          }}
        >
          {props.text2}
        </Text>
      )}
    </View>
  ),
  special: {
    success: (props) => (
      <View
        style={{
          ...baseToastStyle,
          borderLeftColor: colors.primary,
          borderLeftWidth: 5,
          flexDirection: "row",
          paddingVertical: 10,
          shadowOffset: { width: 0, height: 2 },
        }}
      >
        <Image
          source={icons?.AppLogoIconOnly}
          style={commonStyles?.midIcon}
          resizeMode="contain"
        />
        <View style={{ marginLeft: 10 }}>
          <Text
            style={{
              ...baseTextStyle,
              fontFamily: "Metropolis-Medium",
              fontSize: 16,
              marginBottom: 5,
            }}
          >
            {props.text1}
          </Text>
          {props.text2 && (
            <Text
              style={{
                ...baseTextStyle,
                fontFamily: "Metropolis-Regular",
              }}
            >
              {props.text2}
            </Text>
          )}
        </View>
      </View>
    ),
    error: (props) => (
      <View
        style={{
          ...baseToastStyle,
          borderLeftColor: colors.red,
          borderLeftWidth: 5,
          paddingVertical: 10,
          shadowOffset: { width: 0, height: 2 },
        }}
      >
        <Text
          style={{
            ...baseTextStyle,
            fontFamily: "Metropolis-Medium",
            fontSize: 16,
            color: colors.red,
            marginBottom: 5,
          }}
        >
          {props.text1}
        </Text>
        {props.text2 && (
          <Text
            style={{
              ...baseTextStyle,
              fontFamily: "Metropolis-Regular",
              color: colors.gray,
            }}
          >
            {props.text2}
          </Text>
        )}
      </View>
    ),
  },
  tomatoToast: ({ props }) => (
    <View style={props.style}>
      <Text style={props.textStyle}>{props.text}</Text>
    </View>
  ),
};

export const showToast = (type, title, message) => {
  Toast.show({
    type, // e.g., "success", "error", "tomatoToast"
    text1: title,
    text2: message,
    position: type === "success" || type === "error" ? "bottom" : "top",
    visibilityTime: type === "success" || type === "error" ? 2000 : 3000,
  });
};
