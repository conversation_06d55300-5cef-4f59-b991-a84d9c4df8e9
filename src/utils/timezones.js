// List of common timezones for dropdown selection
export const timezones = [
  { value: "UTC-12:00", label: "(UTC-12:00) International Date Line West" },
  { value: "UTC-11:00", label: "(UTC-11:00) Coordinated Universal Time-11" },
  { value: "UTC-10:00", label: "(UTC-10:00) Hawaii" },
  { value: "UTC-09:00", label: "(UTC-09:00) Alaska" },
  { value: "UTC-08:00", label: "(UTC-08:00) Pacific Time (US & Canada)" },
  { value: "UTC-07:00", label: "(UTC-07:00) Mountain Time (US & Canada)" },
  { value: "UTC-06:00", label: "(UTC-06:00) Central Time (US & Canada)" },
  { value: "UTC-05:00", label: "(UTC-05:00) Eastern Time (US & Canada)" },
  { value: "UTC-04:00", label: "(UTC-04:00) Atlantic Time (Canada)" },
  { value: "UTC-03:30", label: "(UTC-03:30) Newfoundland" },
  { value: "UTC-03:00", label: "(UTC-03:00) Brasilia" },
  { value: "UTC-02:00", label: "(UTC-02:00) Mid-Atlantic" },
  { value: "UTC-01:00", label: "(UTC-01:00) Azores" },
  { value: "UTC+00:00", label: "(UTC+00:00) London, Dublin, Edinburgh" },
  { value: "UTC+01:00", label: "(UTC+01:00) Berlin, Paris, Rome, Madrid" },
  { value: "UTC+02:00", label: "(UTC+02:00) Athens, Istanbul, Cairo" },
  { value: "UTC+03:00", label: "(UTC+03:00) Moscow, Riyadh, Kuwait" },
  { value: "UTC+03:30", label: "(UTC+03:30) Tehran" },
  { value: "UTC+04:00", label: "(UTC+04:00) Abu Dhabi, Dubai" },
  { value: "UTC+04:30", label: "(UTC+04:30) Kabul" },
  { value: "UTC+05:00", label: "(UTC+05:00) Islamabad, Karachi" },
  { value: "UTC+05:30", label: "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi" },
  { value: "UTC+05:45", label: "(UTC+05:45) Kathmandu" },
  { value: "UTC+06:00", label: "(UTC+06:00) Astana, Dhaka" },
  { value: "UTC+06:30", label: "(UTC+06:30) Yangon (Rangoon)" },
  { value: "UTC+07:00", label: "(UTC+07:00) Bangkok, Hanoi, Jakarta" },
  { value: "UTC+08:00", label: "(UTC+08:00) Beijing, Hong Kong, Singapore" },
  { value: "UTC+09:00", label: "(UTC+09:00) Tokyo, Seoul" },
  { value: "UTC+09:30", label: "(UTC+09:30) Adelaide" },
  { value: "UTC+10:00", label: "(UTC+10:00) Sydney, Melbourne, Brisbane" },
  { value: "UTC+11:00", label: "(UTC+11:00) Vladivostok" },
  { value: "UTC+12:00", label: "(UTC+12:00) Auckland, Wellington" },
  { value: "UTC+13:00", label: "(UTC+13:00) Nuku'alofa" },
];
