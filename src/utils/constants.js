import { Dimensions } from "react-native";
import icons from "../assets/icons";

export const SCREEN_HEIGHT = Dimensions?.get("screen")?.height;
export const SCREEN_WIDTH = Dimensions?.get("screen")?.width;

export const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

export const maritalStatusOptions = [
  { value: "Single", label: "Single" },
  { value: "Married", label: "Married" },
];

export const sortOptions = [
  { label: "(Alphabetically) A-Z", value: "az" },
  { label: "(Alphabetically) Z-A", value: "za" },
  { label: "Recently Added", value: "recent" },
];

export const filterOptions = [
  { label: "Profiles", value: "profile" },
  { label: "Tags", value: "tag" },
  { label: "City", value: "city" },
  { label: "Favourites", value: "favourites" },
];

export const importOptions = [
  {
    name: "Local Device",
    icon: icons.phoneImportIcon,
    action: "local_device",
  },
  {
    name: "Google",
    icon: icons.googleImportIcon,
    action: "google",
  },
  {
    name: "Microsoft",
    icon: icons.msImportIcon,
    action: "microsoft",
  },
  {
    name: "iCloud",
    icon: icons.icloudImportIcon,
    action: "icloud",
  },
];
export const getProfileIcon = (name) => {
  switch (name.toLowerCase()) {
    case "home":
      return icons.HomeIconProfile;
    case "family":
      return icons.FamilyIconProfile;

    case "work":
      return icons.WorkIconProfile;
    case "tag":
      return icons.tagIcon;
    default:
      return icons.FriendsIconProfile; // fallback icon
  }
};
