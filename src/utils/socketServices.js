import io from 'socket.io-client';


const BASE_URL = 'https://web.ilync.eu:4000'
 



class WSService {

    initializeSocket = async () => {
        try {

            this.socket = io(BASE_URL, {
                transports: ['websocket']
            })
            console.log("initializing socket", this.socket)

            this.socket.on('connect', (data) => {
                console.log("=== socket connected ====")
            })

            this.socket.on('disconnect', (data) => {
                console.log("=== socket disconnected ====")
            })

            this.socket.on('error', (data) => {
                console.log("socket error", data)
            })

        } catch (error) {
            console.log("socket is not inialized", error)
        }
    }

    emit(event, data = {}) {
        this.socket.emit(event, data)
    }
    
    on(event, cb) {
        this.socket.on(event, cb)
    }

    removeListener(listenerName) {
        this.socket.removeListener(listenerName)
    }
    disconnectSocket() {
        if (this.socket) {
            this.socket.disconnect(); // Properly disconnect the socket
            console.log("Socket disconnected");
        } else {
            console.log("No socket to disconnect");
        }
    }

}

const socketServices = new WSService()

export default socketServices