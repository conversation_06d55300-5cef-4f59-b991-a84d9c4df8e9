/**
 * Utility function to map API response data to the format required by updateSharingProfile
 */

/**
 * Maps the provided user information and profile access settings to the format
 * required by the updateSharingProfile API
 *
 * @param {Object} data - The data containing userInfo and getProfileAccess
 * @param {Object} data.userInfo - User information data
 * @param {Object} data.getProfileAccess - Profile access settings
 * @returns {Object} - The formatted data for the updateSharingProfile API
 */
export const mapProfileDataForApi = (data) => {
  const { userInfo, getProfileAccess } = data;

  // Map user information to form fields
  const formData = {
    // Personal Details
    firstName: userInfo.firstName || "",
    lastName: userInfo.lastName || "",
    nickname: userInfo.nickname || "",
    dateOfBirth: userInfo.dateOfBirth || "",
    gender: userInfo.gender || "",
    nationality: userInfo.nationality || "",
    maritalStatus: userInfo.maritalStatus || "",
    spouseName: userInfo.spouseName || "",

    // Contact Information - handle multiple emails and phone numbers
    emails: userInfo.emails || [],
    phoneNumbers: userInfo.phoneNumbers || [],

    // Home Address
    addresses_home: userInfo.addresses_home || {
      apartment: "",
      street: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
    },

    // Other Address
    addresses_other: userInfo.addresses_other || {
      apartment: "",
      street: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
    },

    // Other Details
    personalWebsite: userInfo.personalWebsite || "",
    hobbies: userInfo.hobbies || [],

    // Social Media
    socialMedia: userInfo.socialMedia || {},

    // Emergency Contact
    emergency_contact: userInfo.emergency_contact || {
      contactName: "",
      phoneNumber: "",
      phoneNumber_country_code: "",
      relationship: "",
    },

    // Health Insurance
    healthInsurance: userInfo.healthInsurance || {
      policyNumber: "",
      policyPeriod: "",
      effectiveDate: "",
      expirationDate: "",
      sumInsured: "",
    },

    // Billing Address
    billing_address: userInfo.billing_address || {
      officeBuilding: "",
      street: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
    },

    // Account Details
    account_details: userInfo.account_details || {
      name: "",
      bank: "",
      accountNumber: "",
      ifscCode: "",
      paypalEmail: "",
    },

    // Card Details
    card_details: userInfo.card_details || {
      nameOnCard: "",
      cardNumber: "",
      expiryDate: "",
      cvv: "",
    },

    // Additional fields
    company_logo: userInfo.company_logo || "",
    source: userInfo.source || "",
    sourceId: userInfo.sourceId || "",
  };

  // Map profile access settings to fieldVisibility
  const fieldVisibility = {
    // Personal Details
    firstName_boolean: getProfileAccess.firstName_boolean || false,
    lastName_boolean: getProfileAccess.lastName_boolean || false,
    nickname_boolean: getProfileAccess.nickname_boolean || false,
    dateOfBirth_boolean: getProfileAccess.dateOfBirth_boolean || false,
    gender_boolean: getProfileAccess.gender_boolean || false,
    nationality_boolean: getProfileAccess.nationality_boolean || false,
    maritalStatus_boolean: getProfileAccess.maritalStatus_boolean || false,

    // Contact Information
    emails_boolean: getProfileAccess.emails_boolean || [],
    phoneNumbers_boolean: getProfileAccess.phoneNumbers_boolean || [],

    // Home Address
    addresses_home_boolean: getProfileAccess.addresses_home_boolean || {
      apartment_boolean: false,
      street_boolean: false,
      city_boolean: false,
      state_boolean: false,
      postalCode_boolean: false,
      country_boolean: false,
    },

    // Other Address
    addresses_other_boolean: getProfileAccess.addresses_other_boolean || {
      apartment_boolean: false,
      street_boolean: false,
      city_boolean: false,
      state_boolean: false,
      postalCode_boolean: false,
      country_boolean: false,
    },

    // Other Details
    hobbies_boolean: getProfileAccess.hobbies_boolean || false,
    personalWebsite_boolean: getProfileAccess.personalWebsite_boolean || false,
    religion_boolean: getProfileAccess.religion_boolean || false,
    preferredContactMethod_boolean:
      getProfileAccess.preferredContactMethod_boolean || false,
    timeZone_boolean: getProfileAccess.timeZone_boolean || false,

    // Social Media
    socialMedia_boolean: getProfileAccess.socialMedia_boolean || {},

    // Emergency Contact
    emergency_contact: getProfileAccess.emergency_contact || {
      contactName_boolean: false,
      phoneNumber_boolean: false,
      mobileNumber_boolean: false,
      relationship_boolean: false,
    },

    // Health Insurance
    healthInsurance: getProfileAccess.healthInsurance || {
      policyNumber_boolean: false,
      policyPeriod_boolean: false,
      effectiveDate_boolean: false,
      expirationDate_boolean: false,
      sumInsured_boolean: false,
    },

    // Billing Address
    billing_address_boolean: getProfileAccess.billing_address_boolean || {
      officeBuilding_boolean: false,
      street_boolean: false,
      city_boolean: false,
      state_boolean: false,
      postalCode_boolean: false,
      country_boolean: false,
    },

    // Account Details
    account_details_boolean: getProfileAccess.account_details_boolean || {
      name_boolean: false,
      bank_boolean: false,
      accountNumber_boolean: false,
      ifscCode_boolean: false,
      paypalEmail_boolean: false,
    },

    // Card Details
    card_details_boolean: getProfileAccess.card_details_boolean || {
      nameOnCard_boolean: false,
      cardNumber_boolean: false,
      expiryDate_boolean: false,
      cvv_boolean: false,
    },

    // Additional fields
    profile_image_boolean: getProfileAccess.profile_image_boolean || false,
    company_logo_boolean: getProfileAccess.company_logo_boolean || false,
  };

  // Return just the profile data without the profile_management_id
  return {
    ...formData,
    fieldVisibility,
  };
};
