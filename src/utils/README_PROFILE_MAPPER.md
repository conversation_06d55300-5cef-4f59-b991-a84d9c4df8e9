# Profile Data Mapper Utility

This utility provides functions to map user profile data to the format required by the updateSharingProfile API.

## Overview

The `profileDataMapper.js` utility contains a function called `mapProfileDataForApi` that takes user information and profile access settings and maps them to the format required by the updateSharingProfile API.

## Usage

### Basic Usage

```javascript
import { mapProfileDataForApi } from "../utils/profileDataMapper";
import { updateSharingProfile } from "../redux/features/SharingProfileSlice";

// Example profile data
const profileData = {
  userInfo: {
    // User information fields
    firstName: "John",
    lastName: "Doe",
    emails: [
      {
        address: "<EMAIL>",
        type: "home",
      },
    ],
    // ... other user info fields
  },
  getProfileAccess: {
    // Profile access settings
    firstName_boolean: true,
    lastName_boolean: false,
    // ... other visibility settings
  },
};

// Map the data to the format required by the API
const mappedData = mapProfileDataForApi(profileData);

// Profile management ID (from your app state)
const profile_management_id = "your-profile-management-id";

// Use the mapped data with the updateSharingProfile API
// Note: profile_management_id is passed as a separate parameter
dispatch(
  updateSharingProfile({
    profile_management_id,
    profileData: mappedData,
  })
);
```

### In ProfileCompletionScreen

In the ProfileCompletionScreen, you can use the utility like this:

```javascript
import { mapProfileDataForApi } from "../../utils/profileDataMapper";
import { updateSharingProfile } from "../../redux/features/SharingProfileSlice";

// In your handleNext function
const handleNext = async () => {
  try {
    // Get visibility settings from all sections
    const visibilitySettings = collectVisibilitySettings();

    // Create the user info and profile access data structure
    const profileData = {
      userInfo: {
        ...form,
        // Add any additional fields needed for the API
      },
      getProfileAccess: visibilitySettings,
    };

    // Map the data to the format required by the API
    const mappedData = mapProfileDataForApi(profileData);

    // Get the profile_management_id from your app state
    const profile_management_id = user?._id;

    // Send the mapped data to the API with profile_management_id as a separate parameter
    const response = await dispatch(
      updateSharingProfile({
        profile_management_id, // Pass as a separate parameter
        profileData: mappedData, // Pass the mapped data as profileData
      })
    );

    if (response.payload && response.payload.success) {
      Alert.alert("Success", "Profile updated successfully");
      navigation.navigate("MainApp");
    } else {
      Alert.alert("Error", "Failed to update profile");
    }
  } catch (error) {
    console.error("Error updating profile:", error);
    Alert.alert("Error", "An unexpected error occurred");
  }
};
```

## Data Structure

### Input Structure

The `mapProfileDataForApi` function expects the following input structure:

```javascript
{
  userInfo: {
    // User information fields
    firstName: String,
    lastName: String,
    emails: Array,
    phoneNumbers: Array,
    // ... other user info fields
  },
  getProfileAccess: {
    // Profile access settings (visibility boolean fields)
    firstName_boolean: Boolean,
    lastName_boolean: Boolean,
    // ... other visibility settings
  }
}
```

### Output Structure

The function returns the following structure:

```javascript
{
  // User information fields
  firstName: String,
  lastName: String,
  emails: Array,
  phoneNumbers: Array,
  // ... other user info fields

  // Field visibility settings
  fieldVisibility: {
    firstName_boolean: Boolean,
    lastName_boolean: Boolean,
    // ... other visibility settings
  }
}
```

When calling the `updateSharingProfile` API function, you need to pass this structure along with the profile_management_id as separate parameters:

```javascript
updateSharingProfile({
  profile_management_id: String, // Pass as a separate parameter
  profileData: mappedData, // The output from mapProfileDataForApi
});
```

## Example Components

For complete examples of how to use this utility, see:

- `src/examples/UpdateSharingProfileExample.js` - A reusable component for updating sharing profiles
- `src/examples/UsageExample.js` - An example of how to use the UpdateSharingProfileExample component
