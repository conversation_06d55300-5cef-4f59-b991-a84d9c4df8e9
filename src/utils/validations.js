import { t } from "i18next";

export const validatePasswords = (password, confirmPassword) => {
    const errors = [];
  
    if (!/^(?=.*[a-zA-Z])/.test(password)) {
      errors.push(t('AUTH.passwordMustContainAtLeastOneLetter'));
    }
    if (!/^(?=.*[0-9])/.test(password)) {
      errors.push(t('AUTH.passwordMustContainAtLeastOneNumber'));
    }
    if (!/^(?=.*[!@#$%^&*])/.test(password)) {
      errors.push(t('AUTH.passwordMustContainAtLeastOneSpecialCharacter'));
    }
    if (password.length < 8) {
      errors.push(t('AUTH.passwordMustBeAtLeast8CharactersLong'));
    }
    if (password !== confirmPassword) {
      errors.push(t('AUTH.passwordsDoNotMatch'));
    }
  
    return errors.length ? errors.join(' ') : null;
  };