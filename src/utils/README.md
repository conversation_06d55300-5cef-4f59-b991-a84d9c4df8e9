# Utility Functions Documentation

## Contact Helpers

### `fetchContactsFromSource`

This utility function provides a common way to fetch contacts from different sources (local device, Google, Microsoft, etc.) across the application.

#### Usage

```javascript
import { fetchContactsFromSource } from '../utils/contactHelpers';

// Example usage in a component
const handleFetchContacts = async (source) => {
  // Define success callback
  const onSuccess = (contactsData, sourceType) => {
    // Do something with the contacts data
    console.log(`Fetched ${contactsData.length} contacts from ${sourceType}`);
    
    // Example: Navigate to another screen with the contacts data
    navigation.navigate('ImportContactScreen', {
      contacts: contactsData,
      source: sourceType,
    });
  };
  
  // Define error callback
  const onError = (error) => {
    console.error('Error fetching contacts:', error);
    // Handle the error (show toast, alert, etc.)
  };
  
  // Define loading state setter (optional)
  const setLoading = (isLoading) => {
    // Update loading state in your component
    setIsLoading(isLoading);
  };
  
  // Call the utility function
  await fetchContactsFromSource(
    source,       // 'local_device', 'google', 'microsoft', or 'iCloud'
    onSuccess,    // Success callback
    onError,      // Error callback (optional)
    setLoading    // Loading state setter (optional)
  );
};

// Call the function with the desired source
handleFetchContacts('google');
```

#### Parameters

- `source` (string): The source to fetch contacts from. Possible values:
  - `'local_device'`: Fetch contacts from the device's contact list
  - `'google'`: Fetch contacts from Google Contacts
  - `'microsoft'`: Fetch contacts from Microsoft Outlook/Office 365
  - `'iCloud'`: Fetch contacts from iCloud (not implemented yet)

- `onSuccess` (function): Callback function to be called on successful fetch with contacts data
  - Parameters: `(contactsData, source)`
  - `contactsData`: Array of contacts fetched from the source
  - `source`: The source from which contacts were fetched

- `onError` (function, optional): Callback function to be called on error
  - Parameters: `(error)`
  - `error`: The error object

- `setLoading` (function, optional): Function to set loading state
  - Parameters: `(isLoading)`
  - `isLoading`: Boolean indicating whether the operation is in progress

#### Implementation Details

The function handles:
- Permission checks for local device contacts
- Authentication for Google and Microsoft accounts
- API calls to fetch contacts from each source
- Error handling and loading state management

#### Dependencies

- `react-native-permissions`: For checking and requesting permissions
- `react-native-contacts`: For accessing local device contacts
- `@react-native-google-signin/google-signin`: For Google authentication
- Custom utility functions for Microsoft authentication
