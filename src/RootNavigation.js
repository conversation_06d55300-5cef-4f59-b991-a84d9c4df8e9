import React from "react";
import {
  View,
  Text,
  Button,
  ActivityIndicator,
  Image,
  Pressable,
} from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { createDrawerNavigator } from "@react-navigation/drawer";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import {
  createStackNavigator,
  TransitionPresets,
} from "@react-navigation/stack";
import OnboardingScreen from "./screens/OnboardingScreens/OnboardingScreen";
import LoginScreen from "./screens/LoginScreen/LoginScreen";
import SignupScreen from "./screens/SignupScreen/SignupScreen";
import HomeScreen from "./screens/HomeScreen/HomeScreen";
import WelcomeScreen from "./screens/WelcomeScreen/WelcomeScreen";
import ForgotPasswordScreen from "./screens/ForgetPasswordScreen/ForgetPasswordScreen";
import TwoFactorScreen from "./screens/TwoFactorScreen/TwoFactorScreen";
import PasswordInput from "./components/PasswordInput";
import SignupDetailsScreen from "./screens/SignupScreen/SignupDetailsScreen";
import CustomTabBarButton from "./components/CustomTabBarButton";
import SyncScreen from "./screens/SyncScreen/SyncScreen";
import ConnectScreen from "./screens/ConnectScreen/ConnectScreeen";
import TagScreen from "./screens/TagScreen/TagScreen";
import ProfilesScreen from "./screens/ProfilesScreen/ProfilesScreen";
import icons from "./assets/icons";
import CustomTabBarBackground from "./components/CustomTabBarBackground";
import CustomDrawerContent from "./components/CustomDrawerContent";
import colors from "./assets/colors";
import { useSelector } from "react-redux";
import ImportContactScreen from "./screens/ImportContactScreen/ImportContactScreen";
import SettingsScreen from "./screens/SettingsScreen/SettingsScreen";
import FAQScreen from "./screens/FaqsScreen/FaqsScreen";
import TermsConditions from "./screens/TermsConditions/TermsConditions";
import AboutUs from "./screens/AboutUs/AboutUs";
import PrivacyPolicy from "./screens/PrivacyPolicy/PrivacyPolicy";
import ContactUsScreen from "./screens/ContactUs/ContactUsScreen";
import MyAccountScreen from "./screens/MyAccountScreen/MyAccountScreen";
import ChangePasswordScreen from "./screens/ChangePasswordScreen/ChangePasswordScreen";
import ResetPasswordScreen from "./screens/ForgetPasswordScreen/ResetPasswordScreen";
import NotificationSettingsScreen from "./screens/NotificationSettingsScreen/NotificationSettingsScreen";
import MyText from "./components/MyText";
import commonStyles from "./assets/commonStyles";
import ProfileCompletionScreen from "./screens/ProfileCompletionScreen/ProfileCompletionScreen";
import SelectProfileScreen from "./screens/SetYourProfileScreen/SelectProfileScreen";
import NewTagScreen from "./screens/TagManagement/NewTagScreen";
import AddContactsScreen from "./screens/AddContactsScreen/AddContactsScreen";
import ContactDetailsScreen from "./screens/ContactDetailsScreen/ContactDetailsScreen";
import DuplicateContactScreen from "./screens/DuplicateContactScreen/DuplicateContactScreen";
import TagsScreen from "./screens/TagScreen/TagScreen";
import EditContactScreen from "./screens/ContactDetailsScreen/EditContactScreen";
import AddManualContactScreen from "./screens/AddManualContactScreen/AddManualContactScreen";
import AllProfilesScreen from "./screens/SharingProfile/AllProfilesScreen";
import MoveToProfileScreen from "./screens/SharingProfile/MoveToProfileScreen";
import EditSharingProfileScreen from "./screens/SharingProfile/EditSharingProfileScreen";

const ExploreTrucksScreen = () => (
  <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
    <Text>Explore Trucks</Text>
  </View>
);

const NavigationScreen = () => (
  <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
    <Text>Navigation</Text>
  </View>
);

// ------------------ Navigators ------------------

// Bottom Tabs Navigator
const Tab = createBottomTabNavigator();
function BottomTabs() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarShowLabel: true,

        tabBarStyle: {
          position: "absolute",
          height: 90,
          backgroundColor: "transparent",
          borderTopWidth: 0,
          elevation: 0,
        },

        tabBarBackground: () => <CustomTabBarBackground />,

        tabBarPressColor: "transparent",
        tabBarPressOpacity: 1,

        tabBarButton: (props) => {
          if (route.name === "Connect") {
            return <CustomTabBarButton {...props} />;
          }

          return (
            <Pressable
              {...props}
              android_ripple={{ color: "transparent" }}
              style={props.style}
            >
              {props.children}
            </Pressable>
          );
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <Image
              source={icons.homeIcon}
              style={{
                tintColor: focused ? colors.primary : "#888",
                height: 19,
                width: 19,
              }}
              resizeMode="contain"
            />
          ),
          tabBarLabel: ({ focused }) => (
            <MyText
              children="Home"
              p
              medium
              style={{ color: focused ? colors.primary : "#888" }}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Sync"
        component={SyncScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <Image
              source={icons.syncIcon}
              style={{
                tintColor: focused ? colors.primary : "#888",
                height: 19,
                width: 19,
              }}
              resizeMode="contain"
            />
          ),
          tabBarLabel: ({ focused }) => (
            <MyText
              children="Sync"
              p
              medium
              style={{ color: focused ? colors.primary : "#888" }}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Connect"
        component={ConnectScreen}
        // component={ProfileCompletionScreen}
        options={{
          tabBarLabel: () => null,
          tabBarIcon: ({focused}) => (
            <Image
              source={icons.connectIcon}
              style={{ width: 26, height: 26, 
              tintColor: focused? "#fff" : "#fff"
              }}
              resizeMode="contain"
            />
          ),
          // no tabBarButton here—handled in screenOptions above
        }}
      />

      <Tab.Screen
        name="Tags"
        component={TagScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <Image
              source={icons.tagIcon}
              style={{
                tintColor: focused ? colors.primary : "#888",
                height: 19,
                width: 19,
              }}
              resizeMode="contain"
            />
          ),
          tabBarLabel: ({ focused }) => (
            <MyText
              children="Tags"
              p
              medium
              style={{ color: focused ? colors.primary : "#888" }}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Profiles"
        // component={ProfilesScreen}
        // component={SelectProfileScreen}
        component={AllProfilesScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <Image
              source={icons.profilesIcon}
              style={{
                tintColor: focused ? colors.primary : "#888",
                height: 19,
                width: 19,
              }}
              resizeMode="contain"
            />
          ),
          tabBarLabel: ({ focused }) => (
            <MyText
              children="Profiles"
              p
              medium
              style={{ color: focused ? colors.primary : "#888" }}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

// Drawer Navigator
const Drawer = createDrawerNavigator();
function DrawerNavigator() {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
      }}
      drawerContent={(props) => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen name="Home Tabs" component={BottomTabs} />
      <Drawer.Screen name="My Diary" component={HomeScreen} />
      <Drawer.Screen name="Explore Trucks" component={ExploreTrucksScreen} />
      <Drawer.Screen name="Navigation" component={NavigationScreen} />
    </Drawer.Navigator>
  );
}
const fadeTransition = ({ current }) => ({
  cardStyle: {
    opacity: current.progress,
  },
});

// Stack Navigator for Onboarding and Authentication
const Stack = createStackNavigator();
export default function RootNavigation() {
  const token = useSelector((state) => state.auth.token);

  return (
    <Stack.Navigator
      screenOptions={{
        cardStyleInterpolator: fadeTransition,
        headerShown: false,
      }}
      initialRouteName={token ? "MainApp" : "Onboarding"}
    >
      <Stack.Screen
        name="Onboarding"
        component={OnboardingScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Auth"
        component={AuthStack}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="MainApp"
        component={DrawerNavigator}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="HomeScreen"
        component={HomeScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ImportContactScreen"
        component={ImportContactScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="SettingsScreen"
        component={SettingsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="FaqsScreen"
        component={FAQScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="TermsConditionsScreen"
        component={TermsConditions}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AboutUsScreen"
        component={AboutUs}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="PrivacyPolicyScreen"
        component={PrivacyPolicy}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ContactUsScreen"
        component={ContactUsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="MyAccountScreen"
        component={MyAccountScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ChangePasswordScreen"
        component={ChangePasswordScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="NotificationSettingsScreen"
        component={NotificationSettingsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ProfileCompletionScreen"
        component={ProfileCompletionScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="NewTagScreen"
        component={NewTagScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AddContactsScreen"
        component={AddContactsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ContactDetailsScreen"
        component={ContactDetailsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="DuplicateContactScreen"
        component={DuplicateContactScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="TagsScreen"
        component={TagsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="EditContactScreen"
        component={EditContactScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AddManualContactScreen"
        component={AddManualContactScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AllProfilesScreen"
        component={AllProfilesScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="MoveToProfileScreen"
        component={MoveToProfileScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="EditSharingProfileScreen"
        component={EditSharingProfileScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="SelectProfileScreen"
        component={SelectProfileScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
}

/**
 * ------------------ Authentication Stack Navigator ------------------
 * This stack handles all authentication-related screens such as
 * Login, Forgot Password, Two-Factor Authentication, Signup, etc.
 */
function AuthStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        cardStyleInterpolator: fadeTransition,
      }}
    >
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="TwoFactorScreen"
        component={TwoFactorScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ResetPasswordScreen"
        component={ResetPasswordScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="SignUp"
        component={SignupScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="SignupDetailsScreen"
        component={SignupDetailsScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
}
