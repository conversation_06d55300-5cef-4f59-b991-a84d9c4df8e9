import {
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import React, { useCallback, useState } from "react";

import icons from "../../assets/icons";
import Header from "../../components/Header";
import MyText from "../../components/MyText";
import colors from "../../assets/colors";
import { fetchContactsFromSource } from "../../utils/contactHelpers";
import AppLoader from "../../components/AppLoader";

const SyncContacts = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const contactSources = [
    {
      id: "local_device",
      name: "Local Device",
      icon: icons.localDriveIcon,
      action: () => handleFetchContacts("local_device"),
    },
    {
      id: "google",
      name: "Google",
      icon: icons.googleIcon,
      action: () => handleFetchContacts("google"),
    },
    {
      id: "microsoft",
      name: "Microsoft",
      icon: icons.microsoftIcon,
      action: () => handleFetchContacts("microsoft"),
    },
    {
      id: "iCloud",
      name: "iCloud",
      icon: icons.icloudIcon,
      action: () => handleFetchContacts("iCloud"),
    },
  ];
  const handleFetchContacts = useCallback(
    async (source) => {
      // Define success callback
      const onSuccess = (contactsData, sourceType) => {
        console.log("🚀 ~ onSuccess ~ contactsData:", contactsData.length);
        navigation.navigate("ImportContactScreen", {
          contacts: contactsData,
          source: sourceType,
        });
      };

      // Define error callback
      const onError = (error) => {
        console.error("Error fetching contacts:", error);
      };

      // Use the common utility function
      await fetchContactsFromSource(source, onSuccess, onError, setIsLoading);
    },
    [navigation]
  );
  const renderCard = (item) => (
    <View style={styles.card}>
      <View style={styles.leftSection}>
        <View style={styles.iconWrapper}>
          <Image source={item?.icon} style={styles.sourceIcon} />
        </View>
        <MyText p>{item?.name}</MyText>
      </View>
      <View style={styles.rightSection}>
        <TouchableOpacity style={styles.download} onPress={item?.action}>
          <Image source={icons.download} style={styles.downloadIcon} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.sync}>
          <Image source={icons.sync} style={styles.syncIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: colors.white }}>
      <Header
        title="Sync Contacts"
        leftIcon={icons.backwBg}
        onPressLeft={() => navigation.goBack()}
        pb={20}
        textCenter
      />
      <AppLoader isLoading={isLoading} />
      <ScrollView contentContainerStyle={styles.container}>
        {contactSources.map((item) => (
          <View key={item.id}>{renderCard(item)}</View>
        ))}
      </ScrollView>
    </View>
  );
};

export default SyncContacts;

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  card: {
    backgroundColor: colors.cardBackground || "#fff",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconWrapper: {
    backgroundColor: "#F5F5F5",
    padding: 15,
    borderRadius: 20,
    marginRight: 12,
  },

  sourceIcon: {
    width: 15,
    height: 15,
    resizeMode: "contain",
  },

  rightSection: {
    flexDirection: "row",
  },
  download: {
    backgroundColor: "#D4DAFF",
    padding: 8,
    borderRadius: 20,
    marginLeft: 8,
  },
  downloadIcon: {
    width: 14,
    height: 14,
  },
  sync: {
    backgroundColor: "#F5F5F5",
    padding: 8,
    borderRadius: 20,
    marginLeft: 8,
  },
  syncIcon: {
    width: 14,
    height: 14,
  },
});
