import FastImage from "@d11/react-native-fast-image";
import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  Dimensions,
  StatusBar,
} from "react-native";

const ConnectScreen = () => {
  return (
    <View style={styles.container}>
      {/* <StatusBar barStyle="dark-content" /> */}
      <View style={styles.card}>
        <View style={styles.imageWrapper}>
          <FastImage
            source={{
              uri: "https://plus.unsplash.com/premium_photo-1721830791498-ec809d9d94ec?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
              priority: FastImage.priority.high,
            }}
            style={styles.image}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>
        <Text style={styles.title}>Under Construction</Text>
        <Text style={styles.subtitle}>
          We're working hard to bring you this feature. Stay tuned!
        </Text>
        <Text style={styles.emoji}>🚧 👷‍♂️ 🔧</Text>
      </View>
    </View>
  );
};

const IMAGE_SIZE = Dimensions.get("window").width * 0.5;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f2f2f2",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 30,
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
    elevation: 5,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#333",
    marginTop: 20,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginTop: 10,
  },
  imageWrapper: {
    width: IMAGE_SIZE,
    height: IMAGE_SIZE,
    borderRadius: 20,
    overflow: "hidden",
  },
  image: {
    width: "100%",
    height: "100%",
  },
  emoji: {
    fontSize: 24,
    marginTop: 20,
  },
});

export default ConnectScreen;
