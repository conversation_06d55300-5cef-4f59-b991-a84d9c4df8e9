import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import Header from "../../components/Header";
import PasswordInput from "../../components/PasswordInput";
import icons from "../../assets/icons";
import commonStyles from "../../assets/commonStyles";
import { useDispatch } from "react-redux";
import { changePassword } from "../../redux/features/mainSlice";
import { useNavigation } from "@react-navigation/native";

const ChangePasswordScreen = () => {
  const [form, setForm] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();
  const navigation = useNavigation();

  const handleChange = (field, value) => {
    setForm({ ...form, [field]: value });
    setErrors({ ...errors, [field]: "" });
  };

  const validate = () => {
    let newErrors = {};
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%?&])[A-Za-z\d@$!%?&]{8,}$/;
    if (!form.oldPassword) newErrors.oldPassword = "Required";
    if (!form.newPassword) {
      newErrors.newPassword = "Required";
    } else if (form.newPassword.length < 8) {
      newErrors.newPassword = "Password must be at least 8 characters";
    } else if (!passwordRegex.test(form.newPassword)) {
      newErrors.newPassword =
        "Password must contain at least one uppercase, one lowercase, one number and one special character";
    }

    if (!form.confirmPassword) {
      newErrors.confirmPassword = "Required";
    } else if (form.newPassword !== form.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validate()) return;

    try {
      setLoading(true);
      const payload = {
        currentPassword: form.oldPassword,
        newPassword: form.newPassword,
        confirmPassword: form.confirmPassword,
      };

      const response = await dispatch(changePassword(payload));
      console.log("Response:", JSON.stringify(response));
      if (response.payload.success) {
        navigation.goBack();
        setForm({
          oldPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        setErrors({});
      } else {
        if (
          response.payload.data.message
            .toLowerCase()
            .includes("can not be same")
        ) {
          setErrors({ ...errors, newPassword: response.payload.data.message });
        } else if (
          response.payload.data.message
            .toLowerCase()
            .includes("current password")
        ) {
          setErrors({ ...errors, oldPassword: response.payload.data.message });
        }
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Change Password"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      <ScrollView contentContainerStyle={styles.scroll}>
        <PasswordInput
          label="Old Password*"
          placeholder="Enter old password"
          value={form.oldPassword}
          onChangeText={(val) => handleChange("oldPassword", val)}
          error={errors.oldPassword}
        />

        <PasswordInput
          label="New Password*"
          placeholder="Enter new password"
          value={form.newPassword}
          onChangeText={(val) => handleChange("newPassword", val)}
          error={errors.newPassword}
        />

        <PasswordInput
          label="Re-enter New Password*"
          placeholder="Re-enter new password"
          value={form.confirmPassword}
          onChangeText={(val) => handleChange("confirmPassword", val)}
          error={errors.confirmPassword}
        />

        <TouchableOpacity
          activeOpacity={0.8}
          disabled={loading}
          style={[styles.button, loading && { backgroundColor: "#ccc" }]}
          onPress={handleSubmit}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Submit</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: {
    padding: 20,
  },
  button: {
    marginTop: 30,
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default ChangePasswordScreen;
