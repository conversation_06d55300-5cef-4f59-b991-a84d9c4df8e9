import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
  Image,
  Platform,
} from "react-native";
import BackButton from "../../components/BackButton";
import commonStyles from "../../assets/commonStyles";
import icons from "../../assets/icons";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { useNavigation } from "@react-navigation/native";
import { appleAuth } from "@invertase/react-native-apple-authentication";
import {
  getStoredAppleUserInfo,
  storeAppleUserInfo,
} from "../../utils/commonHelpers";

const providers = [
  {
    label: "Sign Up via Email",
    icon: icons.emailIcon,
    handler: "handleEmailSignup",
  },
  {
    label: "Sign Up via Google",
    icon: icons.googleIcon,
    handler: "handleGoogleSignup",
  },
  {
    label: "Sign Up via Facebook",
    icon: icons.facebookIcon,
    handler: "handleFacebookSignup",
  },
  ...(Platform.OS === "ios"
    ? [
        {
          label: "Sign Up via Apple",
          icon: icons.appleIcon,
          handler: "handleAppleSignup",
        },
      ]
    : []),
];

const SignupScreen = () => {
  const navigation = useNavigation();

  const handleEmailSignup = () => {
    navigation.navigate("SignupDetailsScreen");
  };

  const handleGoogleSignup = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      await GoogleSignin.signOut();
      const userInfo = await GoogleSignin.signIn();
      console.log("Google Sign-In User Info:", userInfo);

      // return;
      const { givenName, familyName, email } = userInfo.data.user;

      navigation.navigate("SignupDetailsScreen", {
        prefillData: {
          firstName: givenName || "",
          middleName: "",
          lastName: familyName || "",
          email: email || "",
          social_login_id: userInfo.data.user.id || "",
        },
      });
    } catch (error) {
      console.error("Google Sign-In Error:", error);
    }
  };

  const handleFacebookSignup = () => {
    console.log("TODO: Add Facebook Signup");
  };

  // const handleAppleSignup = async () => {
  //   const appleAuthRequestResponse = await appleAuth.performRequest({
  //     requestedOperation: appleAuth.Operation.LOGIN,
  //     requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
  //   });

  //   console.log("apple login details : ", appleAuthRequestResponse);
  // };
  const handleAppleSignup = async () => {
    let finalEmail = "";
    let finalFullName = "";
    let userId = "";
    try {
      const response = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      });

      const { user, email, fullName } = response;
      userId = user;
      if (!email || !fullName?.givenName) {
        // fallback to stored data
        const stored = await getStoredAppleUserInfo(user);
        if (stored) {
          finalEmail = stored.email;
          finalFullName = stored.fullName;
          console.log("Loaded from Keychain:", stored);
          navigation.navigate("SignupDetailsScreen", {
          prefillData: {
            firstName: finalFullName?.givenName || "",
            middleName: finalFullName?.middleName || "",
            lastName: finalFullName?.familyName || "",
            email: finalEmail || "",
            social_login_id: user,
          },
        });
        } else {
          console.warn("No stored data found for Apple ID");
        }
      } else {
        // store it for next time
        await storeAppleUserInfo(user, { email, fullName });
        console.log("Stored user data in Keychain");
        finalEmail = email;
        finalFullName = fullName;
        navigation.navigate("SignupDetailsScreen", {
          prefillData: {
            firstName: finalFullName?.givenName || "",
            middleName: finalFullName?.middleName || "",
            lastName: finalFullName?.familyName || "",
            email: finalEmail || "",
            social_login_id: user,
          },
        });
      }
      // Proceed with login using user, finalEmail, finalFullName
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
    }
  };

  const handlers = {
    handleEmailSignup,
    handleGoogleSignup,
    handleFacebookSignup,
    handleAppleSignup,
  };

  return (
    <SafeAreaView style={[commonStyles.safeArea, styles.container]}>
      <View style={styles.innerContainer}>
        <BackButton />

        <Text style={styles.heading}>Let’s get you started</Text>
        <Text style={styles.subheading}>How would you like to sign up?</Text>

        {providers.map((provider, index) => (
          <TouchableOpacity
            key={index}
            style={styles.button}
            onPress={handlers[provider.handler]}
          >
            <Image
              source={provider.icon}
              resizeMode="contain"
              style={styles.icon}
            />
            <Text style={styles.buttonText}>{provider.label}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Already have an account?{" "}
          <Text
            style={styles.loginLink}
            onPress={() => navigation.navigate("Login")}
          >
            Log In
          </Text>
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  innerContainer: { padding: 20 },
  heading: { fontSize: 22, fontWeight: "600", marginTop: 20 },
  subheading: { fontSize: 14, color: "gray", marginVertical: 10 },
  button: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginTop: 10,
  },
  icon: { width: 24, height: 24, marginRight: 12 },
  buttonText: { fontSize: 16, fontWeight: "500" },
  footer: {
    marginBottom: 20,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: 20,
  },
  footerText: { textAlign: "center", fontSize: 14 },
  loginLink: { color: "red", fontWeight: "500" },
});

export default SignupScreen;
