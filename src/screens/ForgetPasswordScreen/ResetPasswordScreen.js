import React, { useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import BackButton from '../../components/BackButton';
import PasswordInput from '../../components/PasswordInput';
import { useDispatch } from 'react-redux';
import { resetPassword } from '../../redux/features/authSlice';

const ResetPasswordScreen = ({ navigation, route }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState({});

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const validate = () => {
    const newErrors = {};

    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%?&])[A-Za-z\d@$!%?&]{8,}$/;
  
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }  else if (!passwordRegex.test(password)) {
    newErrors.password = "Password must contain at least one uppercase, one lowercase, one number and one special character";
  }
  
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Confirmation is required';
    } else if (password && password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
  
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (validate()) {
      const payload = {
        userId: route.params.userId,
        newPassword: password,
      }

      try {
        setLoading(true);
        const response = await dispatch(resetPassword(payload));
        console.log("Response:", JSON.stringify(response));

        if (response.payload.success) {
          navigation.replace('Login');
        }
        else {
          setErrors(prev => ({ ...prev, password: response?.payload?.data?.message }));
        }

      } catch (error) {
        console.error("Error:", error);
        setErrors(prev => ({ ...prev, password: 'An error occurred. Please try again.' }));
      } finally {
        setLoading(false);
      }

    
      
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <BackButton />

        <Text style={styles.title}>Change Password</Text>
        <Text style={styles.subtitle}>You can now change the password</Text>

        <PasswordInput
          label="New Password*"
          placeholder="Enter new password"
          value={password}
          onChangeText={text => {
            setPassword(text);
            if (errors.password) setErrors(prev => ({ ...prev, password: '' }));
          }}
          error={errors.password}
        />

        <PasswordInput
          label="Re-enter New Password*"
          placeholder="Re-enter new password"
          value={confirmPassword}
          onChangeText={text => {
            setConfirmPassword(text);
            if (errors.confirmPassword) setErrors(prev => ({ ...prev, confirmPassword: '' }));
          }}
          error={errors.confirmPassword}
        />

        <TouchableOpacity disabled={loading}  
        style={[styles.submitButton, loading && { backgroundColor: 'gray' }]} 
        activeOpacity={0.8}
        onPress={handleSubmit}>
          {loading ? <ActivityIndicator size="small" color="#fff" /> :
            <Text style={styles.submitText}>Submit</Text>}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  inner: {
    padding: 20,
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginTop: 10,
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 30,
  },
  submitButton: {
    marginTop: 'auto',
    backgroundColor: '#2E64FE',
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
  },
  submitText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default ResetPasswordScreen;
