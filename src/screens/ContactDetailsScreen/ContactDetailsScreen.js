import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  Linking,
} from "react-native";
import colors from "../../assets/colors";
import { useDispatch, useSelector } from "react-redux";
import {
  addtoFavorites,
  getContactDetails,
} from "../../redux/features/contactSlice";
import { showToast } from "../../utils/toastConfig";
import AppLoader from "../../components/AppLoader";
import MyText from "../../components/MyText";
import { PrimaryButton } from "../../components/Button";
import ProfileContainerLayout1 from "./ProfileContainerLayout1";
import ProfileContainerLayout2 from "./ProfileContainerLayout2";
import ProfileContainerLayout3 from "./ProfileContainerLayout3";
import ProfileContainerLayout4 from "./ProfileContainerLayout4";
import InfoContainerLayout1and2 from "./InfoContainerLayout1and2";
import InfoContainerLayout3and4 from "./InfoContainerLayout3and4";
import { useFocusEffect } from "@react-navigation/native";
import { storage } from "../../utils/storage";
import { personalProfile, setSelectedLayout } from "../../redux/features/mainSlice";

const ContactDetailsScreen = ({ route, navigation }) => {
  const [contact, setContact] = useState({});
  const [isFavorite, setIsFavorite] = useState(false);
  const [loading, setLoading] = useState(false);

  const [selectedLayoutOption, setSelectedLayoutOption] = useState(null);
  const [isSortBoxVisible, setSortBoxVisible] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  const selectedLayout = useSelector(
    (state) => state.mainSlice.selectedLayout
  );

  const handleSortOption = (selected) => {
    console.log("Selected sort option:", selected?.value);

    setSelectedLayoutOption(selected?.value);
    setSortBoxVisible(false);
    // storage.set("selectedLayoutOption", selected?.value);
    dispatch(setSelectedLayout(selected?.value));
  };
  const sortOptions = [
    { label: "Layout 1", value: "layout1" },
    { label: "Layout 2", value: "layout2" },
    { label: "Layout 3", value: "layout3" },
    { label: "Layout 4", value: "layout4" },
  ];

  const dispatch = useDispatch();
  const { id, personal } = route.params;

  const toggleFavorite = async () => {
    try {
      setIsFavorite(!isFavorite);
      const payload = {
        is_favorite: !isFavorite,
      };
      const response = await dispatch(addtoFavorites({ id, data: payload }));
      console.log("Response:", response?.payload?.message);
    } catch (error) {
      console.log("Error:", error);
      showToast("error", "Failed to update favorite status");
    }
  };

  useEffect(() => {
    const loadLayoutPreference = () => {
      const savedLayout = selectedLayout;
      // const savedLayout = storage.getString("selectedLayoutOption");
      if (savedLayout && !personal) {
        setSelectedLayoutOption(savedLayout);
      }
    };

    loadLayoutPreference();

    if (id) {
      fetchContact(id);
    }
  }, [route.params]);

  useFocusEffect(
    React.useCallback(() => {
      if (id) {
        fetchContact(id);
      }
    }, [id])
  );

  const fetchContact = async (id) => {
    try {
      setLoading(true);
      const response = personal? await dispatch(personalProfile())  : await dispatch(getContactDetails({ id }));
      if (response.payload.success) {
        console.log("Contact Details:", response.payload.data[0]);

        setContact(response.payload.data[0]);
        setIsFavorite(response.payload.data[0].is_favorite);
      } else {
        showToast("error", response.payload.message);
      }
    } catch (error) {
      showToast("error", "Failed to load contact details");
    } finally {
      setLoading(false);
    }
  };

  const renderDetailItem = (label, value, noLine) => {
    if (!value) return null;

    const isUrl = typeof value === "string" && value.startsWith("http");
    const isPhone = typeof value === "string" && /^[\d\s()+-]+$/.test(value);
    const isEmail = typeof value === "string" && /\S+@\S+\.\S+/.test(value);

    const handlePress = () => {
      if (isUrl) {
        Linking.openURL(value);
      } else if (isPhone) {
        Linking.openURL(`tel:${value}`);
      } else if (isEmail) {
        console.log("Email:", value);

        // return;
        Linking.openURL(`mailto:${value}`);
      }
    };

    const Content = (
      <>
        <MyText bold style={styles.label}>
          {label}
        </MyText>
        <MyText
          style={[
            styles.value,
            (isUrl || isPhone || isEmail) && {
              color: "blue",
              textDecorationLine: "underline",
            },
          ]}
        >
          {value}
        </MyText>
        {!noLine && (
          <View
            style={{
              borderBottomWidth: 1,
              borderBottomColor: "#ccc",
              marginVertical: 10,
            }}
          />
        )}
      </>
    );

    return (
      <View style={styles.detailItem} key={label}>
        {isUrl || isPhone || isEmail ? (
          <TouchableOpacity onPress={handlePress} activeOpacity={0.7}>
            {Content}
          </TouchableOpacity>
        ) : (
          Content
        )}
      </View>
    );
  };
  

  return (
    <View
      style={[
        styles.container,
        selectedLayoutOption === "layout1" || selectedLayoutOption === "layout2"
          ? { backgroundColor: "#fff" }
          : null,
      ]}
    >
      <StatusBar barStyle={scrollY > 150 ? "dark-content" : "light-content"} />
      <AppLoader isLoading={loading} />
      <ScrollView
        contentContainerStyle={styles.scroll}
        onScroll={(event) => {
          // console.log("Scroll Y:", event.nativeEvent.contentOffset.y);
          setScrollY(event.nativeEvent.contentOffset.y);
        }}
        scrollEventThrottle={16}
      >
        {selectedLayoutOption === "layout1" ? (
          <ProfileContainerLayout1
            contact={contact}
            navigation={navigation}
            isSortBoxVisible={isSortBoxVisible}
            setSortBoxVisible={setSortBoxVisible}
            handleSortOption={handleSortOption}
            sortOptions={sortOptions}
            personal={personal}
          />
        ) : selectedLayoutOption === "layout2" ? (
          <ProfileContainerLayout2
            contact={contact}
            navigation={navigation}
            isSortBoxVisible={isSortBoxVisible}
            setSortBoxVisible={setSortBoxVisible}
            handleSortOption={handleSortOption}
            sortOptions={sortOptions}
          />
        ) : selectedLayoutOption === "layout3" ? (
          <ProfileContainerLayout3
            contact={contact}
            navigation={navigation}
            isSortBoxVisible={isSortBoxVisible}
            setSortBoxVisible={setSortBoxVisible}
            handleSortOption={handleSortOption}
            sortOptions={sortOptions}
          />
        ) : selectedLayoutOption === "layout4" ? (
          <ProfileContainerLayout4
            contact={contact}
            navigation={navigation}
            isSortBoxVisible={isSortBoxVisible}
            setSortBoxVisible={setSortBoxVisible}
            handleSortOption={handleSortOption}
            sortOptions={sortOptions}
            toggleFavorite={toggleFavorite}
            isFavorite={isFavorite}
          />
        ) : (
          <ProfileContainerLayout1
            contact={contact}
            navigation={navigation}
            isSortBoxVisible={isSortBoxVisible}
            setSortBoxVisible={setSortBoxVisible}
            handleSortOption={handleSortOption}
            sortOptions={sortOptions}
            personal={personal}
          />
        )}

        {selectedLayoutOption === "layout1" ||
        selectedLayoutOption === "layout2" ? (
          <InfoContainerLayout1and2
            renderDetailItem={renderDetailItem}
            contact={contact}
            toggleFavorite={toggleFavorite}
            isFavorite={isFavorite}
            personal={route.params.personal}
          />
        ) : selectedLayoutOption === "layout3" ||
          selectedLayoutOption === "layout4" ? (
          <InfoContainerLayout3and4
            renderDetailItem={renderDetailItem}
            contact={contact}
            toggleFavorite={toggleFavorite}
            isFavorite={isFavorite}
          />
        ) : (
          <InfoContainerLayout1and2
            renderDetailItem={renderDetailItem}
            contact={contact}
            toggleFavorite={toggleFavorite}
            isFavorite={isFavorite}
            personal={personal}
          />
        )}

        <PrimaryButton
          title={"Sync Contact"}
          // onPress={toggleFavorite}
          style={styles.syncButton}
          // textStyle={styles.syncText}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  scroll: { paddingBottom: 40 },
  profileContainer: { alignItems: "center" },
  profileImage: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height * 0.35,
  },
  info: {
    alignItems: "center",
    backgroundColor: "#00000080",
    position: "absolute",
    bottom: 0,
    width: "100%",
    paddingVertical: 10,
  },
  name: { color: "#fff" },
  subtitle: { color: "#ccc" },
  infoContainer: { padding: 20 },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 10,
    marginVertical: 5,
  },
  sectionTitle: { fontWeight: "600", fontSize: 16 },
  arrow: { height: 16, width: 16 },
  sectionContent: { marginVertical: 8 },
  detailItem: { marginBottom: 8 },
  label: { fontSize: 14 },
  value: { color: colors.black, textTransform: "capitalize" },
  syncButton: {
    marginTop: 20,
  },
  syncText: { color: "#fff", fontWeight: "600", fontSize: 16 },
  optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});

export default ContactDetailsScreen;
