import {
  StyleSheet,
  TouchableOpacity,
  View,
  Dimensions,
  Image,
} from "react-native";
import commonStyles from "../../assets/commonStyles";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import SortOptionsBox from "../../components/SortOptionsBox";
import images from "../../assets/images";
import MyText from "../../components/MyText";
import Header from "../../components/Header";
import ProfilePicture from "../../components/ProfilePicture";

const ProfileContainerLayout2 = ({
  contact,
  navigation,
  isSortBoxVisible,
  setSortBoxVisible,
  handleSortOption,
  sortOptions,
}) => {
  return (
    <View style={styles.profileContainer}>
      <Header
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        rightIcon1={icons.changeLayoutIcon}
        onPressRight={() => setSortBoxVisible((prev) => !prev)}
        // isAvatar={true}
        textCenter={true}
        pb={50}
        noStatusBar={true}
      />
      {isSortBoxVisible && (
        <SortOptionsBox
          options={sortOptions}
          onSelect={handleSortOption}
          style={[styles.sortBoxOverlay, { right: 40 }]}
          optionStyle={styles.sortBoxOption}
          optionTextStyle={styles.optionText}
        />
      )}
      <View style={styles.infoContainer}>
        <ProfilePicture
          uri={contact?.profile_image}
          customStyle={styles.profileImage}
          notEditable={true}
        />
        <MyText bold style={styles.name}>
          {contact?.firstName} {contact?.lastName}
        </MyText>
        <View
          style={[
            commonStyles.rowWithoutSpaceBetween,
            { alignItems: "center", justifyContent: "center" },
          ]}
        >
          <MyText style={styles.subtitle}>Profile:</MyText>
          <MyText style={[styles.subtitle, { color: colors.primary }]}>
            Friends
          </MyText>
        </View>
      </View>
    </View>
  );
};

export default ProfileContainerLayout2;

const screenHeight = Dimensions.get("window").height;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { paddingBottom: 40 },
  profileContainer: { alignItems: "center", marginBottom: screenHeight * 0.25 },
  profileImage: {
    width: Dimensions.get("window").width / 2,
    height: Dimensions.get("window").width / 2,
    borderRadius: Dimensions.get("window").width / 4,
  },
  info: {
    alignItems: "center",
    backgroundColor: "#00000080",
    position: "absolute",
    bottom: 0,
    width: "100%",
    paddingVertical: 10,
  },
  name: {
    color: colors.black,
    textAlign: "center",
    textTransform: "capitalize",
  },
  subtitle: { color: colors.black },
  infoContainer: {
    position: "absolute",
    top: 50,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 10,
    marginVertical: 5,
  },
  sectionTitle: { fontWeight: "600", fontSize: 16 },
  arrow: { height: 16, width: 16 },
  sectionContent: { marginVertical: 8 },
  detailItem: { marginBottom: 8 },
  label: { fontSize: 14 },
  value: { color: colors.black, textTransform: "capitalize" },
  syncButton: {
    marginHorizontal: 20,
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  syncText: { color: "#fff", fontWeight: "600", fontSize: 16 },
  optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});
