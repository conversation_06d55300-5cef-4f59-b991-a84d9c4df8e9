import React, { useState } from 'react';
import { View, TouchableOpacity, Image, StyleSheet } from 'react-native';
import icons from '../../../assets/icons';
// /Users/<USER>/Desktop/projects/ilync_mobile/src/components/MyText.js
import MyText from '../../../components/MyText';

const DropdownSection = ({ title, children }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <View>
      <TouchableOpacity
        onPress={() => setExpanded(!expanded)}
        style={styles.sectionHeader}
      >
        <MyText style={styles.sectionTitle}>{title}</MyText>
        <Image
          source={icons.filledArrowDown}
          style={[
            styles.arrow,
            { transform: [{ rotate: expanded ? '180deg' : '0deg' }] },
          ]}
          resizeMode="contain"
        />
      </TouchableOpacity>
      {expanded && <View style={styles.sectionContent}>{children}</View>}
    </View>
  );
};

const styles = StyleSheet.create({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    marginVertical: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  sectionTitle: { fontWeight: '600', fontSize: 16 },
  arrow: { width: 16, height: 16 },
  sectionContent: { paddingVertical: 10, paddingHorizontal: 5 },
});

export default DropdownSection;
