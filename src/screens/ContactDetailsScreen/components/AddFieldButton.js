import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';

const AddFieldButton = ({ label, onPress }) => (
  <TouchableOpacity onPress={onPress} style={styles.button}>
    <Text style={styles.text}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  button: { marginVertical: 6 },
  text: { color: '#2E64FE', fontSize: 16 },
});

export default AddFieldButton;
