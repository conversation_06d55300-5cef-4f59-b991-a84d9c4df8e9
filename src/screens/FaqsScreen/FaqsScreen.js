import React, { useEffect, useState } from 'react';
import { ActivityIndicator, ScrollView, StyleSheet, View } from 'react-native';
import Header from '../../components/Header';
import FAQItem from './components/FAQItem';
import icons from '../../assets/icons';
import { getFAQS } from '../../redux/features/mainSlice';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

const FAQScreen = () => {
  const [expandedIndexes, setExpandedIndexes] = useState([]);
  const [loading, setLoading] = useState(false);

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [faqs, setFaqs] = useState([
  ]);

  useEffect(() => {
    fetchFAQs();
  }, [])

    const fetchFAQs = async () => {
        try {
            setLoading(true);
            const response = await dispatch(getFAQS());
            console.log("FAQs response:", JSON.stringify(response));
            setFaqs(response?.payload?.data?.faqs);
        } catch (error) {
            console.error('Error fetching FAQs:', error);
        } finally { 
            setLoading(false);
        }
        }

  const toggleItem = (index) => {
    setExpandedIndexes((prev) =>
      prev.includes(index)
        ? prev.filter((i) => i !== index) // Collapse
        : [...prev, index]              // Expand
    );
  };

return (
    <View style={styles.container}>
        <Header
            title="FAQs"
            textCenter
            leftIcon={icons.backButton}
            onPressLeft={() => navigation.goBack()}
            pb={15}
        />

        {loading ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color="#0000ff" />
            </View>
        ) : (
            <ScrollView contentContainerStyle={styles.scroll}>
                {faqs?.map((item, index) => (
                    <FAQItem
                        key={item.id || index}
                        item={item}
                        isExpanded={expandedIndexes.includes(index)}
                        onToggle={() => toggleItem(index)}
                    />
                ))}
            </ScrollView>
        )}
    </View>
);
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  scroll: { paddingVertical: 10 },
});

export default FAQScreen;
