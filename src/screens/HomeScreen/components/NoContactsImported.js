import { Image, StyleSheet, Text, View } from "react-native";
import React from "react";
import images from "../../../assets/images";
import commonStyles from "../../../assets/commonStyles";
import MyText from "../../../components/MyText";
import { SCREEN_WIDTH } from "../../../utils/constants";
import colors from "../../../assets/colors";

const NoContactsImported = () => {
  return (
    <View style={[commonStyles?.center]}>
      <View style={{}}>
        <Image
          source={images?.contactTransferIllustration}
          style={{ height: 241, width: 245 }}
        />
      </View>
      <View style={{ bottom: 12 }}>
        <MyText h5 center medium children={"Contacts Not Imported!"} />
        <MyText
          center
          p
          color={colors.txtGray}
          style={{ width: SCREEN_WIDTH / 1.4, marginTop: 10 }}
          children={
            "Looks like you still have not imported any contacts. Import now to get started"
          }
        />
      </View>
    </View>
  );
};

export default NoContactsImported;

const styles = StyleSheet.create({});
