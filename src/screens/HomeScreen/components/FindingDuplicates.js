import { StyleSheet, View, Image, TouchableOpacity } from "react-native";
import React from "react";
import HorizontalActivityIndicator from "../../../components/HorizontalActivityIndicator";
import colors from "../../../assets/colors";
import MyText from "../../../components/MyText";
import commonStyles from "../../../assets/commonStyles";
import icons from "../../../assets/icons";

const FindingDuplicates = ({
  isLoading,
  duplicatesCount = 2,
  onViewPress,
  progress,
}) => {
  if (isLoading || progress !== undefined) {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: colors?.primaryAlpha(0.1) },
        ]}
      >
        <View style={styles.loadingContainer}>
          <MyText p normal style={{ marginBottom: 5 }}>
            Finding Duplicates
          </MyText>
          <View style={{ paddingVertical: 8 }}>
            <HorizontalActivityIndicator
              isLoading={isLoading}
              progress={progress}
              showPercentage={true}
            />
          </View>
        </View>
      </View>
    );
  }

  if (duplicatesCount > 0) {
    return (
      <View style={styles.container}>
        <View style={styles.alertIconContainer}>
          <View style={styles.alertIcon}>
            <MyText color={colors.white} bold>
              !
            </MyText>
          </View>
        </View>
        <View style={styles.textContainer}>
          <MyText p normal>
            {duplicatesCount} duplicate contacts found
          </MyText>
        </View>
        <TouchableOpacity style={styles.viewButton} onPress={onViewPress}>
          <MyText color={colors.white} p medium>
            View
          </MyText>
          <Image
            source={icons.rightArrowIcon}
            style={[
              commonStyles.extraSmallIcon,
              { tintColor: colors.white, marginLeft: 10 },
            ]}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    );
  }

  return null;
};

export default FindingDuplicates;

const styles = StyleSheet.create({
  loadingContainer: {},
  container: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 231, 231, 1)",
    borderRadius: 10,
    padding: 12,
    marginHorizontal: 15,
    marginVertical: 10,
  },
  alertIconContainer: {
    marginRight: 10,
  },
  alertIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(217, 85, 85, 1)",
    justifyContent: "center",
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
  },
  viewButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
});
