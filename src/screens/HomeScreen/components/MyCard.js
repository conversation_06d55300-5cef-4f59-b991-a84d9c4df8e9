import React from "react";
import { View, Text, Image, StyleSheet, TouchableOpacity } from "react-native";
import Avatar from "../../../components/Avatar";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import MyText from "../../../components/MyText";

const MyCard = ({ onPress }) => {
  const user = useSelector((state) => state.auth.user);
  const fullName = `${user?.firstName} ${user?.middleName || ""} ${
    user?.lastName
  }`;
  const navigation = useNavigation();

  const handlePress = () => {
    // navigation.navigate("ContactDetailsScreen", {
    //       id: item._id,
    //     });
    navigation.navigate("ContactDetailsScreen", {
      id: user?._id,
      personal: true,
    });
  };
  return (
    <View style={styles.card}>
      <Avatar name={fullName} url={user?.profile_image} size={48} />
      <View style={styles.infoContainer}>
        <MyText style={styles.name}>{fullName}</MyText>
        <TouchableOpacity onPress={handlePress}>
          <MyText style={styles.link}>View my card →</MyText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    backgroundColor: "#EAEDFF",
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
    margin: 10,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  infoContainer: {
    marginLeft: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#000",
  },
  link: {
    fontSize: 14,
    color: "#3366FF",
    marginTop: 4,
  },
});

export default MyCard;
