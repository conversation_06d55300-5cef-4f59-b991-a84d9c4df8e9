import React, { useRef, useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Text,
  Alert,
  KeyboardAvoidingView,
  ActivityIndicator,
} from "react-native";
import Header from "../../components/Header";
import InputField from "../../components/InputField";
import PhoneNumberField from "../../components/PhoneNumberField";
import ProfilePicture from "../../components/ProfilePicture";
import icons from "../../assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { editProfile } from "../../redux/features/mainSlice";
import { useNavigation } from "@react-navigation/native";
import { getProfile } from "../../redux/features/authSlice";

const MyAccountScreen = () => {
  const phoneInputRef = useRef(null);
  const user = useSelector((state) => state.auth.user);
  console.log(user, "user");

  const dispatch = useDispatch();
  const navigation = useNavigation();

  const getCallingCode = (cc) => {
    if (!cc) return "";
    return cc.startsWith("+") ? cc.slice(1) : cc;
  };

  const getCountryCode = (iso) => {
    if (!iso) return "";
    return iso.toUpperCase();
  };

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  console.log(user?.calling_code, "user?.calling_code");
  const [form, setForm] = useState({
    firstName: user?.firstName || "",
    middleName: user?.middleName || "",
    lastName: user?.lastName || "",
    email: user?.email || "",
    phone: user?.phone_number || "",
    // strip "+" from "+974" → "974"
    callingCode: getCallingCode(user?.country_code),
    // uppercase (already "QA", but ensures consistency)
    countryCode: getCountryCode(user?.calling_code),
    profileUrl:
      user?.profile_image ||
      "https://ilyncdev.s3.us-east-1.amazonaws.com/uploads/profileImages/doc_1745589197810-233458559.png",
  });

  const handleChange = (key, val) => {
    setForm({ ...form, [key]: val });
  };

  const validate = () => {
    const newErrors = {};

    // First Name
    const first = form.firstName || "";
    if (!first.trim()) {
      newErrors.firstName = "Required";
    } else if (/\d/.test(first)) {
      newErrors.firstName = "First name cannot contain numbers";
    }

    // Middle Name (optional)
    if (form.middleName) {
      if (!form.middleName.trim()) {
        newErrors.middleName = "Middle name cannot be blank";
      } else if (/\d/.test(form.middleName)) {
        newErrors.middleName = "Middle name cannot contain numbers";
      }
    }

    // Last Name
    const last = form.lastName || "";
    if (!last.trim()) {
      newErrors.lastName = "Required";
    } else if (/\d/.test(last)) {
      newErrors.lastName = "Last name cannot contain numbers";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    // Save changes logic here
    if (!validate()) return;
    console.log("Form data:", form);
    try {
      setLoading(true);
      const payload = {
        firstName: form.firstName,
        middleName: form.middleName,
        lastName: form.lastName,
        s3Url: form.profileUrl,
      };
      if (payload.middleName === "") {
        delete payload.middleName;
      }
      const response = await dispatch(editProfile(payload));
      console.log("Response:", JSON.stringify(response));
      if (response?.payload?.success) {
        navigation.goBack();
        dispatch(getProfile());
        Alert.alert("Profile updated successfully");
      } else {
        Alert.alert("Error updating profile");
      }
    } catch (error) {
      console.log(error);

      Alert.alert("Error updating profile");
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView style={{ flex: 1 }} behavior={"padding"}>
      <View style={styles.container}>
        <Header
          title="My Account"
          textCenter
          leftIcon={icons.backButton}
          onPressLeft={() => navigation.goBack()}
          pb={15}
        />

        <ScrollView contentContainerStyle={styles.scroll}>
          <ProfilePicture
            uri={form.profileUrl}
            onUploadSuccess={(url) =>
              setForm((prev) => ({ ...prev, profileUrl: url }))
            }
          />

          <InputField
            label="First Name*"
            value={form.firstName}
            onChangeText={(val) => handleChange("firstName", val)}
            disabled
            // rightIcon={<Image source={icons.editIcon} style={styles.icon} />}
            maxLength={35}
            error={errors.firstName}
          />

          <InputField
            label="Middle Name"
            value={form.middleName}
            onChangeText={(val) => handleChange("middleName", val)}
            disabled
            // rightIcon={<Image source={icons.editIcon} style={styles.icon} />}
            maxLength={35}
            error={errors.middleName}
          />

          <InputField
            label="Last Name*"
            value={form.lastName}
            onChangeText={(val) => handleChange("lastName", val)}
            disabled
            // rightIcon={<Image source={icons.editIcon} style={styles.icon} />}
            maxLength={35}
            error={errors.lastName}
          />

          <InputField
            label="Email*"
            disabled
            value={form.email}
            onChangeText={(val) => handleChange("email", val)}
            // rightIcon={<Image source={icons.edit} style={styles.icon} />}
            error={errors.email}
          />

          {/* <Text style={styles.label}>Phone Number*</Text> */}
          {/* <PhoneNumberField
            value={form.phone}
            phoneInputRef={phoneInputRef}
            onChangeRaw={(val) => handleChange("phone", val)}
            onCodeChange={(val) => handleChange("callingCode", val)}
            onCountryCodeChange={(val) => handleChange("countryCode", val)}
            callingCodeProp={form.callingCode}
            countryCodeProp={form.countryCode}
            disabled
            setError={(msg) => setErrors((prev) => ({ ...prev, phone: msg }))}
            error={errors.phone}
          /> */}
          <InputField
            label="Phone Number*"
            value={`+${form.callingCode}-${form.phone}`}
            onChangeText={(val) => handleChange("phone", val)}
            // rightIcon={<Image source={icons.editIcon} style={styles.icon} />}
            maxLength={35}
            error={errors.phone}
            disabled
          />

          <TouchableOpacity onPress={handleSave} style={styles.button}>
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Save Changes</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: {
    padding: 20,
  },
  icon: {
    width: 18,
    height: 18,
    tintColor: "#999",
  },
  label: {
    fontWeight: "600",
    marginBottom: 6,
    marginTop: 8,
  },
  button: {
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 20,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default MyAccountScreen;
