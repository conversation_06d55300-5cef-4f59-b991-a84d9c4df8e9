import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
} from "react-native";
import RenderHTML from "react-native-render-html";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import { useDispatch } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { getPrivacyPolicy } from "../../redux/features/mainSlice";

const PrivacyPolicy = () => {
  const [loading, setLoading] = useState(false);
  const [PrivacyPolicyContent, setPrivacyPolicyContent] = useState("<p>Lorem ipsum dolor sit amet consectetur...</p>");

  const navigation = useNavigation();
  const dispatch = useDispatch();

  useEffect(() => {
    fetchPrivacyPolicy();
  }, []);

  const fetchPrivacyPolicy = async () => {
    try {
      setLoading(true);
      const response = await dispatch(getPrivacyPolicy()).unwrap();
      console.log("PrivacyPolicy response:", JSON.stringify(response));

      const htmlContent = response?.data?.content || "";

      console.log("PrivacyPolicy HTML content:", htmlContent);
      
      setPrivacyPolicyContent(htmlContent);
    } catch (error) {
      console.error("Error fetching Privacy Policy:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Privacy Policy"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      {loading ? (
        <View style={styles.loaderWrapper}>
          <ActivityIndicator size="large" color="#2E64FE" />
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.scroll}>
          {PrivacyPolicyContent ? (
            <RenderHTML
              contentWidth={Dimensions.get("window").width - 32}
              source={{ html: PrivacyPolicyContent }}
              tagsStyles={{
                p: {
                  fontSize: 14,
                  color: "#333",
                  lineHeight: 22,
                  marginBottom: 10,
                },
              }}
            />
          ) : null}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { padding: 16 },
  loaderWrapper: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default PrivacyPolicy;
