import React, { useState } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import ChipSelector from '../components/ChipSelector';
import icons from '../assets/icons';

const ChipSelectorExample = () => {
  const [selectedTab, setSelectedTab] = useState('home');

  // Define options for the chip selector
  const options = [
    {
      label: 'Home',
      value: 'home',
      icon: <Image source={icons.homeIcon} style={styles.iconStyle} />,
    },
    {
      label: 'Work',
      value: 'work',
      icon: <Image source={icons.workIcon || icons.profilesIcon} style={styles.iconStyle} />,
    },
  ];

  return (
    <View style={styles.container}>
      <ChipSelector
        options={options}
        selectedValue={selectedTab}
        onSelect={(value) => setSelectedTab(value)}
        containerStyle={styles.chipContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  chipContainer: {
    width: '100%',
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
});

export default ChipSelectorExample;
