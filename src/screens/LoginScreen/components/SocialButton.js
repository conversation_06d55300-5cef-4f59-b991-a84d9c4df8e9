import React from 'react';
import { TouchableOpacity, Image, Text, StyleSheet } from 'react-native';

const SocialButton = ({ icon, label, onPress }) => (
  <TouchableOpacity style={styles.button} onPress={onPress}>
    <Image source={icon} resizeMode='contain' style={styles.icon} />
    <Text>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#eee',
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
});

export default SocialButton;
