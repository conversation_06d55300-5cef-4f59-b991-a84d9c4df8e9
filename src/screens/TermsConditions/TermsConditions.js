import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
} from "react-native";
import RenderHTML from "react-native-render-html";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import { getTermsAndConditions } from "../../redux/features/mainSlice";
import { useDispatch } from "react-redux";
import { useNavigation } from "@react-navigation/native";

const TermsConditions = () => {
  const [loading, setLoading] = useState(false);
  const [termsHtml, setTermsHtml] = useState("<p>Lorem ipsum dolor sit amet consectetur...</p>");

  const navigation = useNavigation();
  const dispatch = useDispatch();

  useEffect(() => {
    fetchTermsConditions();
  }, []);

  const fetchTermsConditions = async () => {
    try {
      setLoading(true);
      const response = await dispatch(getTermsAndConditions()).unwrap();
      console.log("TermsConditions response:", JSON.stringify(response));

      const htmlContent = response?.data?.content || "";

      console.log("TermsConditions HTML content:", htmlContent);
      
      setTermsHtml(htmlContent);
    } catch (error) {
      console.error("Error fetching terms & conditions:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Terms & Conditions"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      {loading ? (
        <View style={styles.loaderWrapper}>
          <ActivityIndicator size="large" color="#2E64FE" />
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.scroll}>
          {termsHtml ? (
            <RenderHTML
              contentWidth={Dimensions.get("window").width - 32}
              source={{ html: termsHtml }}
              tagsStyles={{
                p: {
                  fontSize: 14,
                  color: "#333",
                  lineHeight: 22,
                  marginBottom: 10,
                },
              }}
            />
          ) : null}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { padding: 16 },
  loaderWrapper: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default TermsConditions;
