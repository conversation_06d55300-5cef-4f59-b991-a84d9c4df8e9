import FastImage from "@d11/react-native-fast-image";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import commonStyles from "../../../assets/commonStyles";
import icons from "../../../assets/icons";

const ButtonCards = ({ title, subtitle, icon, white, handlePress }) => {
    return (
        <TouchableOpacity activeOpacity={0.7} onPress={handlePress} style={[styles.container, white && {backgroundColor: "#EEF2FF"}]}>
            <FastImage
                style={commonStyles.midIcon}
                source={icon}
                resizeMode={FastImage.resizeMode.contain}
            />
            <Text style={[styles.text, white && {color: "#000"}]}>{title}</Text>
            <Text style={[styles.subtitle, white && {color: "#757575"}]}>{subtitle}</Text>

        </TouchableOpacity>
    );
}

export default ButtonCards;


const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 15,
        backgroundColor: '#2851D3',
        borderRadius: 10,
        gap: 5
    },
    text: {
        fontSize: 16,
        fontWeight: '700',
        color: '#fff',
    },
    subtitle: {
        fontSize: 12,
        color: "#fff",
    }
});