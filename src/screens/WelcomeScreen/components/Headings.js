import { t } from "i18next";
import { StyleSheet, Text, View } from "react-native";

const Headings = ({title, subtitle}) => {
    return (
        <View>
            <Text style={styles.text}>{title}</Text>
            <Text style={styles.subtitle}>{subtitle}</Text>
        </View>
    );
}

export default Headings;



const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 20,
    },
    text: {
        fontSize: 18,
        fontWeight: '700',
    },
    subtitle: {
        fontSize: 14,
        color: "#757575",
        lineHeight: 20,
    }
});