import { Platform, SafeAreaView, StyleSheet, Text, View } from "react-native";
import FastImage from "@d11/react-native-fast-image";
import icons from "../../assets/icons";
import commonStyles from "../../assets/commonStyles";
import { t } from "i18next";
import Headings from "./components/Headings";
import ButtonCards from "./components/ButtonCards";
import { useNavigation } from "@react-navigation/native";

const WelcomeScreen = () => {
    const navigation = useNavigation();
    return (
        <View style={{flex: 1}}>
            <View style={styles.container}>
                <FastImage
                    style={commonStyles.appLogo}
                    source={icons.AppLogo}
                    resizeMode={FastImage.resizeMode.contain}
                />

                <Headings
                    title={t('WELCOME_SCREEN.WelcomeToTruckLogging')}
                    subtitle={t('WELCOME_SCREEN.AreYouReturningUserOrNewToPlatform')} />

                <View style={[commonStyles.rowWithoutSpaceBetween, {marginTop: 100}]}>
                  <ButtonCards 
                    title={t('WELCOME_SCREEN.login')} 
                    subtitle={t('WELCOME_SCREEN.AlreadyHaveAccountLoginToContinue')}
                    handlePress={() => navigation.navigate('Login')}
                    icon={icons.loginIcon} />
                  <ButtonCards 
                    title={t('WELCOME_SCREEN.signup')} 
                    subtitle={t('WELCOME_SCREEN.AlreadyHaveAccountLoginToContinue')}
                    handlePress={() => navigation.navigate('Signup')}
                    icon={icons.signupIcon}
                    white={true} />
                </View>

            </View>
        </View>
    );
}

export default WelcomeScreen;



const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 20,
        backgroundColor: '#fff',
        paddingVertical: Platform.OS =='ios' &&  60
    },
    text: {
        fontSize: 18,
        fontWeight: '700',
    },
    subtitle: {
        fontSize: 14,
        color: "#757575",
        lineHeight: 20,
    }
});