import React from "react";
import { View, Text, StyleSheet, Image } from "react-native";
import icons from "../../../assets/icons";
import commonStyles from "../../../assets/commonStyles";
import Avatar from "../../../components/Avatar";
import { useSelector } from "react-redux";
import MyText from "../../../components/MyText";

const ChatBubble = ({ message, isMe }) => {
  const user = useSelector((state) => state.auth.user);
  return (
    <View
      style={[styles.container, isMe ? styles.rightAlign : styles.leftAlign]}
    >
      <View style={commonStyles.rowWithoutSpaceBetween}>
        {!isMe && <Image source={icons.defaultAvatar} style={styles.icon} />}

        <View
          style={[styles.bubble, isMe ? styles.userBubble : styles.adminBubble]}
        >
          <MyText style={[styles.text, isMe && { color: "#fff" }]}>
            {message.text}
          </MyText>
        </View>

        {isMe && <Avatar url={user?.profile_image} size={25} />}
      </View>

      <MyText style={styles.time}>{message.time}</MyText>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    paddingHorizontal: 16,
  },
  leftAlign: {
    alignItems: "flex-start",
  },
  rightAlign: {
    alignItems: "flex-end",
  },
  bubble: {
    padding: 12,
    borderRadius: 20,
    maxWidth: "75%",
  },
  adminBubble: {
    backgroundColor: "#F2F2F2",
    borderTopLeftRadius: 0,
  },
  userBubble: {
    backgroundColor: "#2E64FE",
    borderTopRightRadius: 0,
  },
  text: {
    fontSize: 14,
  },
  time: {
    fontSize: 11,
    color: "gray",
    marginTop: 4,
  },
  icon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginVertical: 6,
  },
});

export default ChatBubble;
