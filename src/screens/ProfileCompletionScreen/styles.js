import { StyleSheet, Dimensions } from "react-native";
import colors from "../../assets/colors";

const TAB_WIDTH = Dimensions.get("window").width / 3;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  tabsMainContainer: {
    position: "relative",
    marginTop: 18,
  },
  tabsContainer: {
    flexGrow: 0,
  },
  tabButton: {
    width: TAB_WIDTH,
    paddingHorizontal: 18,
    alignItems: "center",
    alignSelf: "center",
    paddingBottom: 6,
  },
  fullLineIndicator: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: colors.inputBg,
  },
  activeIndicator: {
    position: "absolute",
    bottom: 0,
    left: 0,
    width: TAB_WIDTH,
    height: 3,
    borderRadius: 16,
    backgroundColor: colors.primary,
  },
  formContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  label: {
    marginBottom: 6,
    marginTop: 8,
  },
  nextButton: {
    position: "absolute",
    bottom: 20,
    width: "80%",
    alignSelf: "center",
    zIndex: 1,
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  nextButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
