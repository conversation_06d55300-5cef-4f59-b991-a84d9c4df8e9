import React, { useRef, useEffect } from "react";
import { TouchableOpacity, Animated, View } from "react-native";
import PropTypes from "prop-types";
import MyText from "../../../components/MyText";
import icons from "../../../assets/icons";
import commonStyles from "../../../assets/commonStyles";
import colors from "../../../assets/colors";
import CustomCheckbox from "../../../components/CustomCheckbox";

const CollapsibleHeader = ({
  title,
  isCollapsed,
  onToggle,
  showCheckbox = false,
  checkboxText = "",
  isChecked = false,
  onCheckboxToggle = () => {},
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(rotateAnim, {
      toValue: isCollapsed ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isCollapsed]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["-90deg", "0deg"],
  });

  return (
    <TouchableOpacity
      style={[commonStyles?.row, { marginVertical: 12, alignItems: "center" }]}
      onPress={onToggle}
      activeOpacity={1}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <MyText h6 bold>
        {title}
      </MyText>
      <View style={{ flex: 1 }} />
      {showCheckbox && (
        <View
          style={[
            commonStyles.rowWithoutSpaceBetween,
            { marginRight: 8, alignItems: "center" },
          ]}
        >
          <CustomCheckbox checked={isChecked} onChange={onCheckboxToggle} />
          {checkboxText ? <MyText p>{checkboxText}</MyText> : null}
        </View>
      )}
      <Animated.Image
        source={icons.filledArrowDown}
        style={[
          commonStyles?.extraSmallIcon,
          {
            transform: [{ rotate }],
            tintColor: colors.black,
          },
        ]}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );
};

CollapsibleHeader.propTypes = {
  title: PropTypes.string.isRequired,
  isCollapsed: PropTypes.bool.isRequired,
  onToggle: PropTypes.func.isRequired,
  showCheckbox: PropTypes.bool,
  checkboxText: PropTypes.string,
  isChecked: PropTypes.bool,
  onCheckboxToggle: PropTypes.func,
};

export default CollapsibleHeader;
