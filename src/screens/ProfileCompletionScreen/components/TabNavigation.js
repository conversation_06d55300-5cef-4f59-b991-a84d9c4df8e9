import React from "react";
import { View, ScrollView, TouchableOpacity } from "react-native";
import PropTypes from "prop-types";
import Animated from "react-native-reanimated";
import MyText from "../../../components/MyText";
import { styles } from "../styles";

const TabNavigation = ({
  activeSection,
  tabPosition,
  handleTabPress,
  mainSections,
}) => {
  return (
    <View style={styles.tabsMainContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabsContainer}
        contentContainerStyle={{ flexGrow: 0 }}
      >
        {mainSections.map((section, index) => (
          <TouchableOpacity
            key={section.id}
            style={styles.tabButton}
            onPress={() => handleTabPress(section.id, index)}
          >
            <MyText
              p
              semibold={activeSection === section.id}
              style={[{ textAlign: "center", marginBottom: 8 }]}
            >
              {section.title}
            </MyText>
          </TouchableOpacity>
        ))}
      </ScrollView>
      <View style={styles.fullLineIndicator} />
      <Animated.View style={[styles.activeIndicator, tabPosition]} />
    </View>
  );
};

TabNavigation.propTypes = {
  activeSection: PropTypes.string.isRequired,
  tabPosition: PropTypes.object.isRequired,
  handleTabPress: PropTypes.func.isRequired,
  mainSections: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
    })
  ).isRequired,
};

export default TabNavigation;
