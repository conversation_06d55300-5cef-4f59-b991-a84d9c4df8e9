import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Platform,
  Alert,
  Linking,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolateColor,
  Easing,
  runOnJS,
} from "react-native-reanimated";
import commonStyles from "../../assets/commonStyles";
import colors from "../../assets/colors";
import icons from "../../assets/icons";
import AppModal from "../../components/AppModal";
import { check, request, PERMISSIONS, RESULTS } from "react-native-permissions";

import { getPlatformSpecificContactsPermission } from "../../utils/permissionHelper";
import { showToast } from "../../utils/toastConfig";

const { height, width } = Dimensions.get("window");

const WelcomeScreen = ({ navigation }) => {
  const translateY = useSharedValue(height);
  const progress = useSharedValue(0);
  const buttonOpacity = useSharedValue(0);
  const [isContactPermissionModal, setIsContactPermissionModal] =
    useState(false);
  console.log(
    "🚀 ~ WelcomeScreen ~ isContactPermissionModal:",
    isContactPermissionModal
  );

  useEffect(() => {
    checkPermissionAndAnimate();
  }, []);

  // Function to check permission and trigger animations
  const checkPermissionAndAnimate = async () => {
    try {
      const permission = getPlatformSpecificContactsPermission();
      if (!permission) {
        // If no permission is defined for this platform, skip the check
        animateScreen(false);
        return;
      }

      const status = await check(permission);
      console.log("Permission status:", status); // Debug log

      // On Android, we might want to show the modal even if permission is granted
      // to explain why we need it, or only show if denied/blocked
      const shouldShowModal = status !== RESULTS.GRANTED;

      animateScreen(shouldShowModal);
    } catch (error) {
      console.error("Error checking permission:", error);
      // Default to showing the modal if there's an error
      animateScreen(true);
    }
  };

  // Get platform-specific permission

  const animateScreen = (shouldShowModal) => {
    console.log("🚀 ~ animateScreen ~ shouldShowModal:", shouldShowModal);
    translateY.value = withTiming(
      0,
      { duration: 800, easing: Easing.out(Easing.ease) },
      () => {
        progress.value = withTiming(1, { duration: 100 });
        translateY.value = withTiming(
          -height * 1.65,
          { duration: 800, easing: Easing.inOut(Easing.ease) },
          () => {
            progress.value = withTiming(2, { duration: 100 });
            buttonOpacity.value = withTiming(
              1,
              {
                duration: 600,
                easing: Easing.out(Easing.ease),
              },
              (finished) => {
                if (finished && shouldShowModal) {
                  console.log("🚀 ~ animateScreen ~ finished:", finished);
                  runOnJS(setIsContactPermissionModal)(true);
                }
              }
            );
          }
        );
      }
    );
  };

  // Handle contacts permission logic
  const handleContactsPermission = async () => {
    try {
      const permission = getPlatformSpecificContactsPermission();
      const status = await check(permission);

      if (status === RESULTS.GRANTED) {
        showToast("success", "Contacts permission already granted.");
        setIsContactPermissionModal(false);
        return;
      }

      if (status === RESULTS.DENIED) {
        const result = await request(permission);
        handlePermissionResult(result);
      } else if (status === RESULTS.BLOCKED) {
        showToast(
          "error",
          "Contacts permission is blocked. Please enable it in settings."
        );
        handleBlockedPermission();
      }
    } catch (error) {
      console.error("Error requesting contacts permission:", error);
      showToast(
        "error",
        "Error requesting contacts permission. Please try again."
      );
      setIsContactPermissionModal(false);
    }
  };

  // Handle permission result
  const handlePermissionResult = (result) => {
    if (result === RESULTS.GRANTED) {
      showToast("success", "Contacts permission granted.");
      setIsContactPermissionModal(false);
    } else {
      Alert.alert("Error", "Contacts permission denied.", [
        { text: "OK", onPress: () => setIsContactPermissionModal(false) },
        { text: "Settings", onPress: () => Linking.openSettings() },
      ]);
    }
  };

  // Handle blocked permission
  const handleBlockedPermission = () => {
    Alert.alert(
      "Permission Blocked",
      "Contacts permission is blocked. Please enable it in settings.",
      [{ text: "Settings", onPress: () => Linking.openSettings() }]
    );
    setIsContactPermissionModal(false);
  };

  // Animated styles
  const ballAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonOpacity.value,
  }));

  const textAnimatedStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      translateY.value,
      [-height * 1.65, 0],
      ["#000000", "#FFFFFF"]
    );
    return { color };
  });

  console.log(
    "🚀 ~ WelcomeScreen ~ isContactPermissionModal:",
    isContactPermissionModal
  );

  return (
    <>
      <View style={styles.container}>
        <Animated.View style={[styles.ball, ballAnimatedStyle]} />
        <Animated.View style={styles.logo}>
          <Animated.Image
            source={icons.AppLogo}
            resizeMode="contain"
            style={styles.logoImage}
          />
        </Animated.View>

        <View style={styles.content}>
          <Animated.Text style={[styles.title, textAnimatedStyle]}>
            Welcome to iLync
          </Animated.Text>
          <Animated.Text style={[styles.subtitle, textAnimatedStyle]}>
            Effortless Contact Sharing & Networking on the Go.
          </Animated.Text>
        </View>

        <Animated.View style={[styles.buttonContainer, buttonAnimatedStyle]}>
          <TouchableOpacity
            onPress={() => navigation.navigate("Auth", { screen: "Login" })}
            style={[commonStyles.submitButton, styles.button]}
          >
            <Animated.Text style={styles.buttonText}>Login</Animated.Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate("Auth", { screen: "SignUp" })}
            style={[
              commonStyles.submitButton,
              styles.button,
              { backgroundColor: colors.gray },
            ]}
          >
            <Animated.Text
              style={[styles.buttonText, { color: colors.black }]}
              // onPress={() => navigation.navigate("HomeScreen")}
            >
              Sign Up
            </Animated.Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
      <View>
        <AppModal
          title={"Import Contacts"}
          description={
            "We’d like to import your contacts. This allows you to easily find and connect with people you know. You can manage this permission anytime in your settings."
          }
          btnTitle={"Allow"}
          onClose={() => setIsContactPermissionModal(false)}
          visible={isContactPermissionModal}
          onSubmit={() => {
            setIsContactPermissionModal(false);
            handleContactsPermission();
          }}
          cancelText={"May be another time"}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  ball: {
    position: "absolute",
    width: width * 2,
    height: height * 2,
    backgroundColor: colors.primary,
    borderRadius: width,
    alignSelf: "center",
    zIndex: 1,
  },
  content: {
    marginTop: height / 2.5,
    padding: 20,
    zIndex: 2,
    marginHorizontal: 20,
  },
  logo: {
    marginBottom: 20,
    zIndex: 2,
    top: height * 0.2,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  },
  logoImage: {
    height: height * 0.1,
    width: width * 0.4,
    zIndex: 2,
    tintColor: "#fff",
  },
  title: { fontSize: 24, fontWeight: "700", textAlign: "center" },
  subtitle: { fontSize: 14, textAlign: "center", marginTop: 10 },
  buttonContainer: {
    position: "absolute",
    bottom: 40,
    width: "100%",
    alignItems: "center",
    zIndex: 2,
  },
  button: {
    backgroundColor: colors.primary,
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 15,
    marginVertical: 8,
    width: "80%",
  },
  buttonText: { color: "#fff", fontSize: 16, fontWeight: "600" },
});

export default WelcomeScreen;
