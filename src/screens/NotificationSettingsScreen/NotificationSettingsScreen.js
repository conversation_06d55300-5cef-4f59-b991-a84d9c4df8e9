import React, { useState } from "react";
import { View, Text, SafeAreaView, StyleSheet, Image } from "react-native";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import CustomSwitchComponent from "../../components/CustomSwitchComponent";
import commonStyles from "../../assets/commonStyles";
import { useDispatch } from "react-redux";
import { updatePermission } from "../../redux/features/mainSlice";

const NotificationSettingsScreen = ({ navigation }) => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const dispatch = useDispatch();

  const handleToggle = async (newValue) => {
    setNotificationsEnabled(newValue);
    console.log("Notifications Enabled:", newValue);
    try {
      const payload = {
        isNotification: newValue,
      };
      const response = await dispatch(updatePermission(payload));
      console.log("Notification settings response:", JSON.stringify(response));
      if (response.payload.success) {
        console.log("Notification settings updated successfully");
      } else {
        console.log("Failed to update notification settings");
      }
    } catch (error) {
      console.error("Error updating notification settings:", error);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Notification Settings"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      <View style={styles.settingRow}>
        <View style={styles.left}>
          <Image source={icons?.notificationIconWhite} style={[styles.icon]} />
          <View>
            <Text style={styles.label}>Turn ON Notifications</Text>
            <Text style={styles.status}>
              {notificationsEnabled ? "ON" : "OFF"}
            </Text>
          </View>
        </View>

        <CustomSwitchComponent
          label=""
          value={notificationsEnabled}
          onToggle={handleToggle}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  settingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: "center",
  },
  left: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    fontWeight: "600",
    fontSize: 16,
    color: "#000",
    marginBottom: 2,
  },
  status: {
    fontSize: 12,
    color: "gray",
  },
  icon: {
    width: 45,
    height: 45,
    marginRight: 12,
  },
});

export default NotificationSettingsScreen;
