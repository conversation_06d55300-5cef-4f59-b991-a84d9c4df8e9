import React from "react";
import { View, Safe<PERSON>reaView, FlatList, StyleSheet } from "react-native";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import FloatingPlusButton from "../../components/FloatingPlusButton";
import MyText from "../../components/MyText";
import TagCard from "../TagScreen/components/TagCard";
import { getProfileNameData } from "../../redux/features/SharingProfileSlice";
import { useDispatch, useSelector } from "react-redux";
import {
  setProfileContactsData,
  setSelectedContactsForProfile,
} from "../../redux/features/mainSlice";
import { useFocusEffect } from "@react-navigation/native";
import AppLoader from "../../components/AppLoader";

const AllProfilesScreen = ({ navigation }) => {
  const [sharingProfile, setSharingProfile] = React.useState([]);
  const [refreshing, setRefreshing] = React.useState(false);
  const dispatch = useDispatch();

  const getProfileNameDataState = useSelector(
    (state) => state.sharingProfileSlice.getProfileNameData
  );
  const { data: allProfileNameData, error, loading } = getProfileNameDataState;

  // Only use useFocusEffect for fetching
  useFocusEffect(
    React.useCallback(() => {
      dispatch(getProfileNameData());
    }, [dispatch]) // add dispatch to dependencies
  );

  React.useEffect(() => {
    const tempDefault = Array.isArray(allProfileNameData?.data?.result)
      ? allProfileNameData.data.result.map((profile) => ({
          id: profile._id,
          name: profile.profile_name,
          users: profile.members.length,
          members: profile.members,
          contacts: profile.contacts,
        }))
      : [];
    setSharingProfile(tempDefault);
    // Debug log
    console.log("AllProfilesScreen mapped sharingProfile:", tempDefault);
  }, [allProfileNameData]);

  const handleAdd = () => {
    dispatch(setProfileContactsData([]));
    navigation.navigate("SelectProfileScreen");
  };
  const handleEdit = (profileId) => {
    navigation.navigate("MoveToProfileScreen", {
      profileId: profileId,
    });
  };
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      await dispatch(getProfileNameData());
    } finally {
      setRefreshing(false);
    }
  }, [dispatch]);

  // Show fallback UI if error
  if (error) {
    return (
      <View style={styles.container}>
        <Header
          title="Sharing Profile"
          textCenter
          leftIcon={icons.backButton}
          onPressLeft={() => navigation.goBack()}
          pb={15}
        />
        <View style={styles.innercontainer}>
          <MyText style={{ color: "red", textAlign: "center", marginTop: 40 }}>
            Failed to load profiles. Please try again.
          </MyText>
        </View>
        <FloatingPlusButton onPress={handleAdd} />
      </View>
    );
  }
  function handleNavigate(profileId) {
    dispatch(setProfileContactsData([]));
    navigation.navigate("EditSharingProfileScreen", { profileId });
  }
  return (
    <View style={styles.container}>
      <Header
        title="Sharing Profile"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <AppLoader isLoading={loading} />
      <View style={styles.innercontainer}>
        <FlatList
          data={Array.isArray(sharingProfile) ? sharingProfile : []}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item) => item.id?.toString?.() || String(item.id)}
          renderItem={({ item }) => (
            <TagCard
              tag={item}
              onEdit={() => handleEdit(item?.id)}
              onContainerPress={() => handleNavigate(item?.id)}
              iconName={item?.name?.toLowerCase()}
            />
          )}
          contentContainerStyle={styles.list}
          ListEmptyComponent={
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                height: 500,
              }}
            >
              <MyText>No Sharing Profile Found</MyText>
            </View>
          }
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>
      <FloatingPlusButton onPress={handleAdd} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  innercontainer: { marginTop: 10, flex: 1 },
  list: {
    paddingBottom: 190, // Ensures last FlatList item is visible above FloatingPlusButton
  },
});

export default AllProfilesScreen;
