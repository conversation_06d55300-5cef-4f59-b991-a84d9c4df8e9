import React from "react";
import { <PERSON>, <PERSON><PERSON>rea<PERSON><PERSON><PERSON>, <PERSON><PERSON>ist, StyleSheet } from "react-native";
import TagCard from "./components/TagCard";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import FloatingPlusButton from "../../components/FloatingPlusButton";
import { useDispatch } from "react-redux";
import { getTags, setTagsData } from "../../redux/features/mainSlice";
import MyText from "../../components/MyText";
import { useFocusEffect } from "@react-navigation/native";
import AppLoader from "../../components/AppLoader";

const TagsScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const [tags, setTags] = React.useState([]);
  const [loading, setLoading] = React.useState(false);

  const handleEdit = (tag) => {
    navigation.navigate("NewTagScreen", {
      id: tag.id,
    });
  };

  const fetchAllTags = async () => {
    try {
      setLoading(true);
      const response = await dispatch(getTags());
      console.log("Tags:", response.payload.data);
      if (response.payload.success) {
        const tempTags = response.payload.data.result.map((tag) => ({
          id: tag._id,
          name: tag.tag_name,
          users: tag.members.length,
          members: tag.members,
        }));
        setTags(tempTags);
      } else {
        console.error("Failed to fetch tags:", response.payload.data.message);
      }
    } catch (error) {
      console.error("Error fetching tags:", error);
    } finally {
      setLoading(false);
    }
  };
  useFocusEffect(
    React.useCallback(() => {
      fetchAllTags();
      dispatch(setTagsData({ contacts: [], tagName: "" }));
    }, [])
  );

  const handleAdd = () => {
    navigation.navigate("NewTagScreen");
  };

  return (
    <View style={styles.container}>
      <Header
        title="Tags"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      <FlatList
        data={tags}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <TagCard tag={item} onEdit={() => handleEdit(item)} iconName="tag" />
        )}
        contentContainerStyle={styles.list}
        ListEmptyComponent={
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              height: 500,
            }}
          >
            <MyText>No Tags Found</MyText>
          </View>
        }
      />

      <FloatingPlusButton onPress={handleAdd} />
      <AppLoader isLoading={loading} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  list: {
    paddingBottom: 80,
  },
});

export default TagsScreen;
