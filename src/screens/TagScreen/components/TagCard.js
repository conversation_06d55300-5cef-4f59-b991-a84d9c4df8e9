import React from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet } from "react-native";
import icons from "../../../assets/icons";
import colors from "../../../assets/colors";
import commonStyles from "../../../assets/commonStyles";
import MyText from "../../../components/MyText";
import { getProfileIcon } from "../../../utils/constants";

const TagCard = ({ tag, onContainerPress, onEdit, iconName = "tag" }) => {
  return (
    <TouchableOpacity
      style={styles.card}
      onPress={onContainerPress ? onContainerPress : onEdit}
    >
      <View style={styles.left}>
        <View
          style={{
            width: 56,
            height: 56,
            marginRight: 10,
            backgroundColor: colors.secondary,
            borderRadius: 50,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Image
            source={getProfileIcon(iconName)}
            resizeMode="contain"
            style={[styles.icon]}
          />
        </View>
        <View>
          <MyText style={styles.title}>{tag?.name}</MyText>
          <MyText style={styles.subtitle}>{tag?.users} users</MyText>
        </View>
      </View>
      <TouchableOpacity onPress={onEdit}>
        <View style={styles.editWrapper}>
          <Image source={icons.editIcon} style={styles.editIcon} />
          <MyText p style={styles.editText}>
            Edit
          </MyText>
        </View>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#fff",
    padding: 15,
    // marginHorizontal: 20,
    marginVertical: 5,
    borderRadius: 10,
    // elevation: 1,
  },
  left: {
    flexDirection: "row",
    alignItems: "center",
  },
  icon: {
    width: 22,
    height: 22,
    tintColor: colors.primary,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000",
  },
  subtitle: {
    fontSize: 12,
    color: "#888",
  },
  editWrapper: {
    flexDirection: "row",
    alignItems: "center",
  },
  editIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
    tintColor: colors.primaryColor,
  },
  editText: {
    fontSize: 14,
    color: colors.primaryColor,
  },
});

export default TagCard;
