import axios from "axios";
import { Alert } from "react-native";
import store from "../redux/store";
import { logoutUser } from "../redux/features/authSlice";
import { CommonActions } from "@react-navigation/native";
import { navigationRef } from "../utils/NavigationService";
import { showToast } from "../utils/toastConfig";

// export const BASE_URL = "http://98.85.8.57:4000/api/v1/user";
export const BASE_URL = "https://web.ilync.eu:4000/api/v1/user";
// export const BASE_URL = "https://ilync.eu:4000/api/v1/user";

// Flags to avoid duplicate alerts
let isNetworkAlertShown = false;
let isUnauthorizedAlertShown = false;

// === MAIN API INSTANCE ===
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
});

// === REQUEST INTERCEPTOR ===
api.interceptors.request.use(
  async (config) => {
    const state = store.getState();
    const token = state.auth.token;
    console.log("Token in axios interceptor:", token);
    
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    config.headers["Content-Type"] = "application/json";
    return config;
  },
  (error) => Promise.reject(error)
);

// === RESPONSE INTERCEPTOR ===
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const { config, response, message } = error;

    // --- Network error (no response) ---
    if (message === "Network Error") {
      if (!config?.suppressNetworkAlert && !isNetworkAlertShown) {
        isNetworkAlertShown = true;
        // Alert.alert(
        //   "Network Error",
        //   "Please check your internet connection and try again.",
        //   [
        //     {
        //       text: "OK",
        //       onPress: () => {
        //         isNetworkAlertShown = false;
        //       },
        //     },
        //   ],
        //   { cancelable: false }
        // );
        showToast(
          "error",
          "Network Error",
          "Please check your internet connection and try again."
        );
        setTimeout(() => {
          isNetworkAlertShown = false;
        }, 1000);
      }
      return Promise.reject(error);
    }

    // --- Unauthorized error ---
    if (response?.status === 401) {
      if (!isUnauthorizedAlertShown) {
        isUnauthorizedAlertShown = true;
        Alert.alert(
          "Unauthorized access",
          "Please login again",
          [
            {
              text: "OK",
              onPress: () => {
                isUnauthorizedAlertShown = false;
                store.dispatch(logoutUser());
                if (navigationRef.isReady()) {
                  navigationRef.dispatch(
                    CommonActions.reset({
                      index: 0,
                      routes: [{ name: "Auth" }],
                    })
                  );
                }
              },
            },
          ],
          { cancelable: false }
        );
      }
    }

    return Promise.reject(error);
  }
);

// === MULTIPART API INSTANCE ===
const multipartApi = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
});

// === MULTIPART REQUEST INTERCEPTOR ===
multipartApi.interceptors.request.use(
  async (config) => {
    const state = store.getState();
    const token = state.auth.token;

    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    config.headers["Accept"] = "application/json";
    config.headers["Content-Type"] = "multipart/form-data";
    return config;
  },
  (error) => Promise.reject(error)
);

// === EXPORTS ===
export { api, multipartApi };
