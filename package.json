{"name": "iLync", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "npmI": "npm i --legacy-peer-deps"}, "dependencies": {"@d11/react-native-fast-image": "^8.9.2", "@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "i18next": "^24.2.3", "react": "19.0.0", "react-hook-form": "^7.56.2", "react-i18next": "^15.4.1", "react-native": "0.79.0", "react-native-collapsible": "^1.6.2", "react-native-contacts": "^8.0.5", "react-native-country-picker-modal": "^2.0.0", "react-native-dotenv": "^3.4.11", "react-native-fbsdk-next": "^13.4.1", "react-native-gesture-handler": "^2.25.0", "react-native-image-crop-picker": "^0.42.0", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.12.2", "react-native-mmkv-storage": "^0.11.2", "react-native-modal-datetime-picker": "^18.0.0", "react-native-msal": "^4.0.4", "react-native-permissions": "^5.3.0", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "^3.17.3", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.0", "@react-native/eslint-config": "0.79.0", "@react-native/metro-config": "0.79.0", "@react-native/typescript-config": "0.79.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}